import 'package:json_annotation/json_annotation.dart';

part 'call.g.dart';

/// Enum for call types
enum CallType {
  @JsonValue('voice')
  voice,
  @JsonValue('video')
  video,
}

/// Enum for call states
enum CallState {
  @JsonValue('idle')
  idle,
  @JsonValue('outgoing')
  outgoing,
  @JsonValue('incoming')
  incoming,
  @JsonValue('ringing')
  ringing,
  @JsonValue('connecting')
  connecting,
  @JsonValue('connected')
  connected,
  @JsonValue('ended')
  ended,
  @JsonValue('declined')
  declined,
  @JsonValue('missed')
  missed,
  @JsonValue('failed')
  failed,
}

/// Enum for call end reasons
enum CallEndReason {
  @JsonValue('user_ended')
  userEnded,
  @JsonValue('remote_ended')
  remoteEnded,
  @JsonValue('declined')
  declined,
  @JsonValue('missed')
  missed,
  @JsonValue('network_error')
  networkError,
  @JsonValue('permission_denied')
  permissionDenied,
  @JsonValue('timeout')
  timeout,
}

/// Model for WebRTC signaling data
@JsonSerializable()
class SignalingData {
  final String type; // offer, answer, ice-candidate
  final Map<String, dynamic> data;
  final String? sdp;
  final String? candidate;
  final int? sdpMLineIndex;
  final String? sdpMid;

  const SignalingData({
    required this.type,
    required this.data,
    this.sdp,
    this.candidate,
    this.sdpMLineIndex,
    this.sdpMid,
  });

  factory SignalingData.fromJson(Map<String, dynamic> json) =>
      _$SignalingDataFromJson(json);

  Map<String, dynamic> toJson() => _$SignalingDataToJson(this);
}

/// Model for call participant
@JsonSerializable()
class CallParticipant {
  final String userId;
  final String name;
  final String? avatarUrl;
  final bool isAudioMuted;
  final bool isVideoMuted;
  final DateTime joinedAt;
  final DateTime? leftAt;

  const CallParticipant({
    required this.userId,
    required this.name,
    this.avatarUrl,
    this.isAudioMuted = false,
    this.isVideoMuted = false,
    required this.joinedAt,
    this.leftAt,
  });

  factory CallParticipant.fromJson(Map<String, dynamic> json) =>
      _$CallParticipantFromJson(json);

  Map<String, dynamic> toJson() => _$CallParticipantToJson(this);

  CallParticipant copyWith({
    String? userId,
    String? name,
    String? avatarUrl,
    bool? isAudioMuted,
    bool? isVideoMuted,
    DateTime? joinedAt,
    DateTime? leftAt,
  }) {
    return CallParticipant(
      userId: userId ?? this.userId,
      name: name ?? this.name,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      isAudioMuted: isAudioMuted ?? this.isAudioMuted,
      isVideoMuted: isVideoMuted ?? this.isVideoMuted,
      joinedAt: joinedAt ?? this.joinedAt,
      leftAt: leftAt ?? this.leftAt,
    );
  }
}

/// Main call model
@JsonSerializable()
class Call {
  final String id;
  final String conversationId;
  final CallType type;
  final CallState state;
  final String callerId;
  final String calleeId;
  final List<CallParticipant> participants;
  final DateTime createdAt;
  final DateTime? startedAt;
  final DateTime? endedAt;
  final int? duration; // in seconds
  final CallEndReason? endReason;
  final SignalingData? signalingData;
  final Map<String, dynamic>? metadata;

  const Call({
    required this.id,
    required this.conversationId,
    required this.type,
    required this.state,
    required this.callerId,
    required this.calleeId,
    required this.participants,
    required this.createdAt,
    this.startedAt,
    this.endedAt,
    this.duration,
    this.endReason,
    this.signalingData,
    this.metadata,
  });

  factory Call.fromJson(Map<String, dynamic> json) => _$CallFromJson(json);

  Map<String, dynamic> toJson() => _$CallToJson(this);

  Call copyWith({
    String? id,
    String? conversationId,
    CallType? type,
    CallState? state,
    String? callerId,
    String? calleeId,
    List<CallParticipant>? participants,
    DateTime? createdAt,
    DateTime? startedAt,
    DateTime? endedAt,
    int? duration,
    CallEndReason? endReason,
    SignalingData? signalingData,
    Map<String, dynamic>? metadata,
  }) {
    return Call(
      id: id ?? this.id,
      conversationId: conversationId ?? this.conversationId,
      type: type ?? this.type,
      state: state ?? this.state,
      callerId: callerId ?? this.callerId,
      calleeId: calleeId ?? this.calleeId,
      participants: participants ?? this.participants,
      createdAt: createdAt ?? this.createdAt,
      startedAt: startedAt ?? this.startedAt,
      endedAt: endedAt ?? this.endedAt,
      duration: duration ?? this.duration,
      endReason: endReason ?? this.endReason,
      signalingData: signalingData ?? this.signalingData,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if this is an incoming call for the given user
  bool isIncomingFor(String userId) {
    return calleeId == userId && 
           (state == CallState.incoming || state == CallState.ringing);
  }

  /// Check if this is an outgoing call for the given user
  bool isOutgoingFor(String userId) {
    return callerId == userId && 
           (state == CallState.outgoing || state == CallState.ringing);
  }

  /// Check if the call is active (connected)
  bool get isActive => state == CallState.connected;

  /// Check if the call is ended
  bool get isEnded => [
    CallState.ended,
    CallState.declined,
    CallState.missed,
    CallState.failed,
  ].contains(state);

  /// Get the other participant's ID
  String getOtherParticipantId(String currentUserId) {
    return callerId == currentUserId ? calleeId : callerId;
  }

  /// Get call duration in formatted string
  String get formattedDuration {
    if (duration == null) return '00:00';
    
    final minutes = duration! ~/ 60;
    final seconds = duration! % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
