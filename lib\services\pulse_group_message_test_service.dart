import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:pulsemeet/services/conversation_service.dart';
import 'package:pulsemeet/services/automatic_conversation_service.dart';

/// Simple test service to verify pulse group message delivery
class PulseGroupMessageTestService {
  static final PulseGroupMessageTestService _instance =
      PulseGroupMessageTestService._internal();
  factory PulseGroupMessageTestService() => _instance;
  PulseGroupMessageTestService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final ConversationService _conversationService = ConversationService();
  final AutomaticConversationService _autoConversationService =
      AutomaticConversationService();

  /// Test sending a message to a pulse group and verify it appears on other devices
  Future<bool> testPulseGroupMessageDelivery(String pulseId) async {
    try {
      debugPrint('🧪 Testing pulse group message delivery for pulse: $pulseId');

      // Get the conversation
      final conversation =
          await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for pulse: $pulseId');
        return false;
      }

      debugPrint('✅ Found conversation: ${conversation.id}');

      // Send a test message
      final testContent =
          'TEST MESSAGE - ${DateTime.now().millisecondsSinceEpoch}';
      debugPrint('📤 Sending test message: $testContent');

      final sentMessage = await _conversationService.sendTextMessage(
        conversation.id,
        testContent,
      );

      if (sentMessage == null) {
        debugPrint('❌ Failed to send test message');
        return false;
      }

      debugPrint('✅ Test message sent successfully: ${sentMessage.id}');

      // Wait a moment for real-time delivery
      await Future.delayed(const Duration(seconds: 2));

      // Verify the message exists in the database
      final verificationResponse = await _supabase
          .from('messages')
          .select()
          .eq('conversation_id', conversation.id)
          .eq('content', testContent)
          .limit(1);

      if (verificationResponse.isEmpty) {
        debugPrint('❌ Test message not found in database');
        return false;
      }

      final dbMessage = verificationResponse.first;
      debugPrint('✅ Test message verified in database: ${dbMessage['id']}');
      debugPrint('📊 Message details:');
      debugPrint('   - ID: ${dbMessage['id']}');
      debugPrint('   - Content: ${dbMessage['content']}');
      debugPrint('   - Sender: ${dbMessage['sender_id']}');
      debugPrint('   - Status: ${dbMessage['status']}');
      debugPrint('   - Created: ${dbMessage['created_at']}');

      return true;
    } catch (e) {
      debugPrint('❌ Error testing pulse group message delivery: $e');
      return false;
    }
  }

  /// Test the RPC function directly
  Future<bool> testRPCFunction(String pulseId) async {
    try {
      debugPrint('🧪 Testing RPC function for pulse: $pulseId');

      // Get the conversation
      final conversation =
          await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for pulse: $pulseId');
        return false;
      }

      final currentUserId = _supabase.auth.currentUser?.id;
      if (currentUserId == null) {
        debugPrint('❌ No current user');
        return false;
      }

      // Test the RPC function directly
      final testContent =
          'RPC TEST MESSAGE - ${DateTime.now().millisecondsSinceEpoch}';
      debugPrint('📤 Testing RPC function with message: $testContent');

      final messageId =
          await _supabase.rpc('send_pulse_group_message', params: {
        'conversation_id': conversation.id,
        'sender_id': currentUserId,
        'content': testContent,
        'message_type': 'text',
        'media_data': null,
        'location_data': null,
        'is_encrypted': false,
        'encryption_metadata': null,
      });

      if (messageId == null) {
        debugPrint('❌ RPC function returned null');
        return false;
      }

      debugPrint('✅ RPC function returned message ID: $messageId');

      // Verify the message exists
      final verificationResponse = await _supabase
          .from('messages')
          .select()
          .eq('id', messageId)
          .limit(1);

      if (verificationResponse.isEmpty) {
        debugPrint('❌ RPC message not found in database');
        return false;
      }

      final dbMessage = verificationResponse.first;
      debugPrint('✅ RPC message verified in database');
      debugPrint('📊 RPC Message details:');
      debugPrint('   - ID: ${dbMessage['id']}');
      debugPrint('   - Content: ${dbMessage['content']}');
      debugPrint('   - Sender: ${dbMessage['sender_id']}');
      debugPrint('   - Status: ${dbMessage['status']}');
      debugPrint('   - Created: ${dbMessage['created_at']}');

      return true;
    } catch (e) {
      debugPrint('❌ Error testing RPC function: $e');
      return false;
    }
  }

  /// Run comprehensive test
  Future<TestResults> runComprehensiveTest(String pulseId) async {
    final results = TestResults();

    try {
      debugPrint(
          '🧪 Running comprehensive pulse group message test for pulse: $pulseId');

      // Test 1: RPC Function
      debugPrint('🧪 Test 1: RPC Function');
      results.rpcFunctionTest = await testRPCFunction(pulseId);

      // Test 2: ConversationService
      debugPrint('🧪 Test 2: ConversationService');
      results.conversationServiceTest =
          await testPulseGroupMessageDelivery(pulseId);

      // Test 3: Real-time subscription
      debugPrint('🧪 Test 3: Real-time subscription');
      results.realtimeTest = await _testRealtimeSubscription(pulseId);

      results.calculateOverallSuccess();

      debugPrint('🧪 Comprehensive test completed');
      debugPrint('📊 Overall success rate: ${results.overallSuccessRate}%');

      return results;
    } catch (e) {
      debugPrint('❌ Error during comprehensive test: $e');
      return results;
    }
  }

  /// Test real-time subscription
  Future<bool> _testRealtimeSubscription(String pulseId) async {
    try {
      debugPrint('🧪 Testing real-time subscription for pulse: $pulseId');

      final conversation =
          await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for real-time test');
        return false;
      }

      // Set up a real-time listener
      final completer = Completer<bool>();
      bool messageReceived = false;

      final testContent =
          'REALTIME TEST - ${DateTime.now().millisecondsSinceEpoch}';

      // Subscribe to real-time updates
      final channel = _supabase.channel('test_realtime_${conversation.id}');

      channel.on(
        RealtimeListenTypes.postgresChanges,
        ChannelFilter(
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: 'conversation_id=eq.${conversation.id}',
        ),
        (payload, [ref]) {
          final messageData = payload['new'] as Map<String, dynamic>?;
          if (messageData != null &&
              messageData['content'] == testContent &&
              !messageReceived) {
            messageReceived = true;
            debugPrint('✅ Real-time message received: ${messageData['id']}');
            if (!completer.isCompleted) {
              completer.complete(true);
            }
          }
        },
      );

      channel.subscribe();

      // Wait for subscription to be ready
      await Future.delayed(const Duration(seconds: 1));

      // Send test message
      final sentMessage = await _conversationService.sendTextMessage(
        conversation.id,
        testContent,
      );

      if (sentMessage == null) {
        debugPrint('❌ Failed to send real-time test message');
        await channel.unsubscribe();
        return false;
      }

      // Wait for real-time delivery
      final result = await completer.future.timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          debugPrint('⏰ Real-time test timed out');
          return false;
        },
      );

      await channel.unsubscribe();

      if (result) {
        debugPrint('✅ Real-time subscription test passed');
      } else {
        debugPrint('❌ Real-time subscription test failed');
      }

      return result;
    } catch (e) {
      debugPrint('❌ Error testing real-time subscription: $e');
      return false;
    }
  }

  /// Quick test for a specific pulse group
  static Future<void> quickTest(String pulseId) async {
    final testService = PulseGroupMessageTestService();
    debugPrint('🧪 Running quick test for pulse: $pulseId');

    final results = await testService.runComprehensiveTest(pulseId);
    final report = testService.generateTestReport(results);

    debugPrint('📊 Test Results:');
    debugPrint(report);
  }

  /// Generate test report
  String generateTestReport(TestResults results) {
    final buffer = StringBuffer();
    buffer.writeln('🧪 PULSE GROUP MESSAGE TEST REPORT');
    buffer.writeln('==================================');
    buffer.writeln('');
    buffer.writeln('📊 Overall Success Rate: ${results.overallSuccessRate}%');
    buffer.writeln('');
    buffer.writeln('📋 Test Results:');
    buffer.writeln(
        '• RPC Function Test: ${results.rpcFunctionTest ? '✅ PASS' : '❌ FAIL'}');
    buffer.writeln(
        '• ConversationService Test: ${results.conversationServiceTest ? '✅ PASS' : '❌ FAIL'}');
    buffer.writeln(
        '• Real-time Subscription Test: ${results.realtimeTest ? '✅ PASS' : '❌ FAIL'}');
    buffer.writeln('');

    buffer.writeln('🎯 Status:');
    if (results.allTestsPassed) {
      buffer.writeln(
          '🎉 All tests passed! Pulse group messaging is working correctly.');
    } else {
      buffer.writeln('⚠️ Some tests failed. Check the logs for details.');
      buffer.writeln('');
      buffer.writeln('🔧 Troubleshooting:');
      if (!results.rpcFunctionTest) {
        buffer.writeln('• Check RPC function permissions and parameters');
      }
      if (!results.conversationServiceTest) {
        buffer.writeln('• Check ConversationService message sending logic');
      }
      if (!results.realtimeTest) {
        buffer.writeln(
            '• Check Supabase real-time configuration and subscriptions');
      }
    }

    return buffer.toString();
  }
}

/// Test results container
class TestResults {
  bool rpcFunctionTest = false;
  bool conversationServiceTest = false;
  bool realtimeTest = false;
  double overallSuccessRate = 0.0;

  void calculateOverallSuccess() {
    final scores = [
      rpcFunctionTest ? 100.0 : 0.0,
      conversationServiceTest ? 100.0 : 0.0,
      realtimeTest ? 100.0 : 0.0,
    ];

    overallSuccessRate = scores.reduce((a, b) => a + b) / scores.length;
  }

  bool get allTestsPassed =>
      rpcFunctionTest && conversationServiceTest && realtimeTest;
}
