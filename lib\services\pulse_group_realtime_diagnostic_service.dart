import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:pulsemeet/services/conversation_service.dart';
import 'package:pulsemeet/services/automatic_conversation_service.dart';
import 'package:pulsemeet/services/pulse_group_encryption_service.dart';
import 'package:pulsemeet/models/conversation.dart';
import 'package:pulsemeet/models/message.dart';

/// Comprehensive diagnostic service for pulse group real-time messaging issues
class PulseGroupRealtimeDiagnosticService {
  static final PulseGroupRealtimeDiagnosticService _instance =
      PulseGroupRealtimeDiagnosticService._internal();
  factory PulseGroupRealtimeDiagnosticService() => _instance;
  PulseGroupRealtimeDiagnosticService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final ConversationService _conversationService = ConversationService();
  final AutomaticConversationService _autoConversationService =
      AutomaticConversationService();
  final PulseGroupEncryptionService _encryptionService =
      PulseGroupEncryptionService();

  /// Run comprehensive diagnostic for pulse group messaging
  Future<DiagnosticResults> runComprehensiveDiagnostic(String pulseId) async {
    final results = DiagnosticResults();

    try {
      debugPrint(
          '🔍 Starting comprehensive pulse group messaging diagnostic for pulse: $pulseId');

      // Step 1: Check conversation existence and participants
      results.conversationCheck = await _checkConversationSetup(pulseId);

      // Step 2: Test message insertion
      results.messageInsertion = await _testMessageInsertion(pulseId);

      // Step 3: Test real-time subscriptions
      results.realtimeSubscription = await _testRealtimeSubscriptions(pulseId);

      // Step 4: Test encryption/decryption
      results.encryptionTest = await _testEncryptionDecryption(pulseId);

      // Step 5: Test participant permissions
      results.participantPermissions =
          await _testParticipantPermissions(pulseId);

      // Step 6: Test cross-device delivery simulation
      results.crossDeviceDelivery = await _testCrossDeviceDelivery(pulseId);

      debugPrint('🔍 Comprehensive diagnostic completed');
      debugPrint('📊 Results: ${results.getSuccessRate()}% success rate');

      return results;
    } catch (e) {
      debugPrint('❌ Error during comprehensive diagnostic: $e');
      return results;
    }
  }

  /// Check conversation setup and participants
  Future<bool> _checkConversationSetup(String pulseId) async {
    try {
      debugPrint('🔍 Checking conversation setup for pulse: $pulseId');

      // Check if conversation exists
      final conversation =
          await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for pulse');
        return false;
      }

      debugPrint('✅ Found conversation: ${conversation.id}');

      // Check participants
      final participantsResponse = await _supabase
          .from('conversation_participants')
          .select()
          .eq('conversation_id', conversation.id);

      debugPrint('👥 Found ${participantsResponse.length} participants');

      // Check pulse participants
      final pulseParticipantsResponse = await _supabase
          .from('pulse_participants')
          .select()
          .eq('pulse_id', pulseId)
          .eq('status', 'active');

      debugPrint(
          '🎯 Found ${pulseParticipantsResponse.length} active pulse participants');

      if (participantsResponse.length != pulseParticipantsResponse.length) {
        debugPrint('⚠️ Mismatch between conversation and pulse participants');
        // Try to sync participants
        await _syncParticipants(pulseId, conversation.id);
      }

      return true;
    } catch (e) {
      debugPrint('❌ Error checking conversation setup: $e');
      return false;
    }
  }

  /// Test message insertion
  Future<bool> _testMessageInsertion(String pulseId) async {
    try {
      debugPrint('🔍 Testing message insertion for pulse: $pulseId');

      final conversation =
          await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for message insertion test');
        return false;
      }

      final testMessage =
          'Test message ${DateTime.now().millisecondsSinceEpoch}';

      // Test direct message insertion
      final insertedMessage = await _supabase
          .from('messages')
          .insert({
            'conversation_id': conversation.id,
            'sender_id': _supabase.auth.currentUser?.id,
            'content': testMessage,
            'message_type': 'text',
            'status': 'sent',
          })
          .select()
          .single();

      if (insertedMessage != null) {
        debugPrint('✅ Message inserted successfully: ${insertedMessage['id']}');

        // Verify message can be retrieved
        final retrievedMessage = await _supabase
            .from('messages')
            .select()
            .eq('id', insertedMessage['id'])
            .single();

        if (retrievedMessage != null) {
          debugPrint('✅ Message retrieval successful');
          return true;
        } else {
          debugPrint('❌ Message retrieval failed');
          return false;
        }
      } else {
        debugPrint('❌ Message insertion failed');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error testing message insertion: $e');
      return false;
    }
  }

  /// Test real-time subscriptions
  Future<bool> _testRealtimeSubscriptions(String pulseId) async {
    try {
      debugPrint('🔍 Testing real-time subscriptions for pulse: $pulseId');

      final conversation =
          await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for real-time test');
        return false;
      }

      // Test subscription setup
      final completer = Completer<bool>();
      bool messageReceived = false;

      // Set up real-time listener
      final channel = _supabase.channel('test_realtime_${conversation.id}');

      channel.on(
        RealtimeListenTypes.postgresChanges,
        ChannelFilter(
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: 'conversation_id=eq.${conversation.id}',
        ),
        (payload, [ref]) {
          debugPrint('📡 Real-time message received: ${payload['new']?['id']}');
          messageReceived = true;
          if (!completer.isCompleted) {
            completer.complete(true);
          }
        },
      );

      channel.subscribe();

      // Insert a test message
      await Future.delayed(const Duration(milliseconds: 500));

      final testMessage =
          'Real-time test ${DateTime.now().millisecondsSinceEpoch}';
      await _supabase.from('messages').insert({
        'conversation_id': conversation.id,
        'sender_id': _supabase.auth.currentUser?.id,
        'content': testMessage,
        'message_type': 'text',
        'status': 'sent',
      });

      // Wait for real-time notification
      final result = await completer.future.timeout(
        const Duration(seconds: 5),
        onTimeout: () => false,
      );

      await channel.unsubscribe();

      if (result && messageReceived) {
        debugPrint('✅ Real-time subscription working correctly');
        return true;
      } else {
        debugPrint('❌ Real-time subscription failed');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error testing real-time subscriptions: $e');
      return false;
    }
  }

  /// Test encryption and decryption
  Future<bool> _testEncryptionDecryption(String pulseId) async {
    try {
      debugPrint('🔍 Testing encryption/decryption for pulse: $pulseId');

      // Create test message
      final testMessage = Message(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        conversationId: 'test_conversation',
        senderId: _supabase.auth.currentUser?.id ?? 'test_user',
        messageType: MessageType.text,
        content: 'Test encryption message',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Test encryption
      final encryptedMessage = await _encryptionService
          .encryptMessageForPulseGroup(testMessage, pulseId);

      // Test decryption
      final decryptedMessage = await _encryptionService
          .decryptMessageFromPulseGroup(encryptedMessage, pulseId);

      if (decryptedMessage.content == testMessage.content) {
        debugPrint('✅ Encryption/decryption working correctly');
        return true;
      } else {
        debugPrint('❌ Encryption/decryption failed');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error testing encryption/decryption: $e');
      return false;
    }
  }

  /// Test participant permissions
  Future<bool> _testParticipantPermissions(String pulseId) async {
    try {
      debugPrint('🔍 Testing participant permissions for pulse: $pulseId');

      final conversation =
          await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for permissions test');
        return false;
      }

      // Test if current user can read messages
      final messages = await _supabase
          .from('messages')
          .select()
          .eq('conversation_id', conversation.id)
          .limit(1);

      debugPrint(
          '✅ User can read messages: ${messages.length} messages accessible');

      // Test if current user can insert messages
      final testInsert = await _supabase.from('messages').insert({
        'conversation_id': conversation.id,
        'sender_id': _supabase.auth.currentUser?.id,
        'content': 'Permission test message',
        'message_type': 'text',
        'status': 'sent',
      }).select();

      if (testInsert.isNotEmpty) {
        debugPrint('✅ User can insert messages');
        return true;
      } else {
        debugPrint('❌ User cannot insert messages');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error testing participant permissions: $e');
      return false;
    }
  }

  /// Test cross-device delivery simulation
  Future<bool> _testCrossDeviceDelivery(String pulseId) async {
    try {
      debugPrint(
          '🔍 Testing cross-device delivery simulation for pulse: $pulseId');

      final conversation =
          await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for cross-device test');
        return false;
      }

      // Subscribe to conversation messages
      await _conversationService.subscribeToMessages(conversation.id);

      // Send a message through ConversationService
      final sentMessage = await _conversationService.sendTextMessage(
        conversation.id,
        'Cross-device test message ${DateTime.now().millisecondsSinceEpoch}',
      );

      if (sentMessage != null) {
        debugPrint('✅ Message sent through ConversationService');

        // Wait a bit for real-time propagation
        await Future.delayed(const Duration(seconds: 2));

        // Check if message appears in database
        final dbMessage = await _supabase
            .from('messages')
            .select()
            .eq('id', sentMessage.id)
            .maybeSingle();

        if (dbMessage != null) {
          debugPrint('✅ Message found in database');
          return true;
        } else {
          debugPrint('❌ Message not found in database');
          return false;
        }
      } else {
        debugPrint('❌ Failed to send message through ConversationService');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error testing cross-device delivery: $e');
      return false;
    }
  }

  /// Sync participants between pulse and conversation
  Future<void> _syncParticipants(String pulseId, String conversationId) async {
    try {
      debugPrint('🔄 Syncing participants for pulse: $pulseId');

      // Get pulse participants
      final pulseParticipants = await _supabase
          .from('pulse_participants')
          .select('user_id')
          .eq('pulse_id', pulseId)
          .eq('status', 'active');

      // Get conversation participants
      final conversationParticipants = await _supabase
          .from('conversation_participants')
          .select('user_id')
          .eq('conversation_id', conversationId);

      final pulseUserIds = pulseParticipants.map((p) => p['user_id']).toSet();
      final conversationUserIds =
          conversationParticipants.map((p) => p['user_id']).toSet();

      // Add missing participants to conversation
      final missingInConversation =
          pulseUserIds.difference(conversationUserIds);
      for (final userId in missingInConversation) {
        await _supabase.from('conversation_participants').insert({
          'conversation_id': conversationId,
          'user_id': userId,
          'role': 'member',
        });
        debugPrint('➕ Added user $userId to conversation');
      }

      debugPrint('✅ Participant sync completed');
    } catch (e) {
      debugPrint('❌ Error syncing participants: $e');
    }
  }

  /// Generate diagnostic report
  String generateDiagnosticReport(DiagnosticResults results) {
    final buffer = StringBuffer();
    buffer.writeln('🔍 PULSE GROUP MESSAGING DIAGNOSTIC REPORT');
    buffer.writeln('==========================================');
    buffer.writeln('');
    buffer.writeln('📊 Overall Success Rate: ${results.getSuccessRate()}%');
    buffer.writeln('');
    buffer.writeln('📋 Individual Test Results:');
    buffer.writeln(
        '• Conversation Setup: ${results.conversationCheck ? '✅ PASS' : '❌ FAIL'}');
    buffer.writeln(
        '• Message Insertion: ${results.messageInsertion ? '✅ PASS' : '❌ FAIL'}');
    buffer.writeln(
        '• Real-time Subscription: ${results.realtimeSubscription ? '✅ PASS' : '❌ FAIL'}');
    buffer.writeln(
        '• Encryption Test: ${results.encryptionTest ? '✅ PASS' : '❌ FAIL'}');
    buffer.writeln(
        '• Participant Permissions: ${results.participantPermissions ? '✅ PASS' : '❌ FAIL'}');
    buffer.writeln(
        '• Cross-device Delivery: ${results.crossDeviceDelivery ? '✅ PASS' : '❌ FAIL'}');
    buffer.writeln('');
    buffer.writeln('🎯 Recommendations:');

    if (!results.conversationCheck) {
      buffer.writeln(
          '• Check automatic conversation creation and participant sync');
    }
    if (!results.messageInsertion) {
      buffer.writeln('• Verify database permissions and RLS policies');
    }
    if (!results.realtimeSubscription) {
      buffer.writeln(
          '• Check Supabase real-time configuration and subscriptions');
    }
    if (!results.encryptionTest) {
      buffer.writeln('• Verify encryption service and key management');
    }
    if (!results.participantPermissions) {
      buffer.writeln('• Check user permissions and conversation access');
    }
    if (!results.crossDeviceDelivery) {
      buffer.writeln(
          '• Verify ConversationService message sending and real-time delivery');
    }

    return buffer.toString();
  }
}

/// Diagnostic results container
class DiagnosticResults {
  bool conversationCheck = false;
  bool messageInsertion = false;
  bool realtimeSubscription = false;
  bool encryptionTest = false;
  bool participantPermissions = false;
  bool crossDeviceDelivery = false;

  int getSuccessCount() {
    int count = 0;
    if (conversationCheck) count++;
    if (messageInsertion) count++;
    if (realtimeSubscription) count++;
    if (encryptionTest) count++;
    if (participantPermissions) count++;
    if (crossDeviceDelivery) count++;
    return count;
  }

  double getSuccessRate() {
    return (getSuccessCount() / 6.0 * 100).roundToDouble();
  }

  bool get allTestsPassed => getSuccessCount() == 6;
}
