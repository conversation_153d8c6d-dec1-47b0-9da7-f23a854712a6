import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:pulsemeet/services/encryption_service.dart';
import 'package:pulsemeet/models/message.dart';
import 'package:pulsemeet/models/encryption_key.dart';

/// Enhanced encryption service specifically for pulse group conversations
/// Handles group key management and multi-participant encryption
class PulseGroupEncryptionService {
  static final PulseGroupEncryptionService _instance =
      PulseGroupEncryptionService._internal();
  factory PulseGroupEncryptionService() => _instance;
  PulseGroupEncryptionService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final EncryptionService _encryptionService = EncryptionService();

  // Cache for pulse group keys
  final Map<String, GroupEncryptionKey> _groupKeyCache = {};

  /// Encrypt message for pulse group
  Future<Message> encryptMessageForPulseGroup(
      Message message, String pulseId) async {
    try {
      debugPrint('🔐 Encrypting message for pulse group: $pulseId');

      // Get or create group encryption key
      final groupKey = await _getOrCreateGroupKey(pulseId);
      if (groupKey == null) {
        debugPrint('❌ Failed to get group encryption key');
        return message; // Return unencrypted message as fallback
      }

      // Encrypt message content
      final encryptedContent = await _encryptContent(message.content, groupKey);

      // Encrypt media data if present
      Map<String, dynamic>? encryptedMediaData;
      if (message.mediaData != null) {
        encryptedMediaData =
            await _encryptMediaData(message.mediaData!, groupKey);
      }

      // Encrypt location data if present
      Map<String, dynamic>? encryptedLocationData;
      if (message.locationData != null) {
        encryptedLocationData =
            await _encryptLocationData(message.locationData!, groupKey);
      }

      // Create encryption metadata
      final encryptionMetadata = {
        'algorithm': 'AES-256-GCM',
        'key_id': groupKey.keyId,
        'pulse_id': pulseId,
        'version': groupKey.version,
        'encrypted_at': DateTime.now().toIso8601String(),
      };

      return message.copyWith(
        content: encryptedContent,
        mediaData: encryptedMediaData != null
            ? MediaData.fromJson(encryptedMediaData)
            : null,
        locationData: encryptedLocationData != null
            ? LocationData.fromJson(encryptedLocationData)
            : null,
        isEncrypted: true,
        encryptionMetadata: encryptionMetadata,
        keyVersion: groupKey.version,
      );
    } catch (e) {
      debugPrint('❌ Error encrypting message for pulse group: $e');
      return message; // Return unencrypted message as fallback
    }
  }

  /// Decrypt message from pulse group
  Future<Message> decryptMessageFromPulseGroup(
      Message message, String pulseId) async {
    try {
      if (!message.isEncrypted || message.encryptionMetadata == null) {
        return message; // Already decrypted or not encrypted
      }

      debugPrint('🔓 Decrypting message from pulse group: $pulseId');

      final keyId = message.encryptionMetadata!['key_id'] as String?;
      if (keyId == null) {
        debugPrint('❌ No key ID in encryption metadata');
        return message;
      }

      // Get group encryption key
      final groupKey = await _getGroupKey(pulseId, keyId);
      if (groupKey == null) {
        debugPrint('❌ Failed to get group encryption key for decryption');
        return message;
      }

      // Decrypt message content
      final decryptedContent = await _decryptContent(message.content, groupKey);

      // Decrypt media data if present
      MediaData? decryptedMediaData;
      if (message.mediaData != null) {
        decryptedMediaData =
            await _decryptMediaData(message.mediaData!, groupKey);
      }

      // Decrypt location data if present
      LocationData? decryptedLocationData;
      if (message.locationData != null) {
        decryptedLocationData =
            await _decryptLocationData(message.locationData!, groupKey);
      }

      return message.copyWith(
        content: decryptedContent,
        mediaData: decryptedMediaData,
        locationData: decryptedLocationData,
        isEncrypted: false,
        encryptionMetadata: null,
      );
    } catch (e) {
      debugPrint('❌ Error decrypting message from pulse group: $e');
      return message; // Return encrypted message if decryption fails
    }
  }

  /// Get or create group encryption key for pulse
  Future<GroupEncryptionKey?> _getOrCreateGroupKey(String pulseId) async {
    try {
      // Check cache first
      final cacheKey = 'pulse_$pulseId';
      if (_groupKeyCache.containsKey(cacheKey)) {
        return _groupKeyCache[cacheKey];
      }

      // Try to get existing key from database
      final existingKey = await _getGroupKey(pulseId, null);
      if (existingKey != null) {
        _groupKeyCache[cacheKey] = existingKey;
        return existingKey;
      }

      // Create new group key
      final newKey = await _createGroupKey(pulseId);
      if (newKey != null) {
        _groupKeyCache[cacheKey] = newKey;
      }

      return newKey;
    } catch (e) {
      debugPrint('❌ Error getting or creating group key: $e');
      return null;
    }
  }

  /// Get group encryption key from database
  Future<GroupEncryptionKey?> _getGroupKey(
      String pulseId, String? keyId) async {
    try {
      final query =
          _supabase.from('pulse_group_keys').select().eq('pulse_id', pulseId);

      if (keyId != null) {
        query.eq('key_id', keyId);
      }

      final response = await query.maybeSingle();

      if (response != null) {
        return GroupEncryptionKey.fromJson(response);
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error getting group key from database: $e');
      return null;
    }
  }

  /// Create new group encryption key
  Future<GroupEncryptionKey?> _createGroupKey(String pulseId) async {
    try {
      debugPrint('🔑 Creating new group encryption key for pulse: $pulseId');

      // Generate new encryption key using ConversationKey
      final conversationKey = await _encryptionService.generateConversationKey(
        'pulse_$pulseId',
        ConversationType.pulse,
      );

      final keyId = DateTime.now().millisecondsSinceEpoch.toString();

      final groupKey = GroupEncryptionKey(
        keyId: keyId,
        pulseId: pulseId,
        keyData: conversationKey.symmetricKey,
        version: 1,
        createdAt: DateTime.now(),
        isActive: true,
      );

      // Store in database
      await _supabase.from('pulse_group_keys').insert(groupKey.toJson());

      debugPrint('✅ Group encryption key created successfully');
      return groupKey;
    } catch (e) {
      debugPrint('❌ Error creating group key: $e');
      return null;
    }
  }

  /// Encrypt content using group key
  Future<String> _encryptContent(
      String content, GroupEncryptionKey groupKey) async {
    try {
      // Create a ConversationKey for encryption
      final conversationKey = ConversationKey(
        keyId: groupKey.keyId,
        conversationId: 'pulse_${groupKey.pulseId}',
        conversationType: ConversationType.pulse,
        symmetricKey: groupKey.keyData,
        createdAt: groupKey.createdAt,
      );

      final encryptedData = await _encryptionService.encryptData(
        Uint8List.fromList(utf8.encode(content)),
        conversationKey,
      );
      return base64Encode(encryptedData.ciphertext);
    } catch (e) {
      debugPrint('❌ Error encrypting content: $e');
      return content; // Return original content as fallback
    }
  }

  /// Decrypt content using group key
  Future<String> _decryptContent(
      String encryptedContent, GroupEncryptionKey groupKey) async {
    try {
      // Create a ConversationKey for decryption
      final conversationKey = ConversationKey(
        keyId: groupKey.keyId,
        conversationId: 'pulse_${groupKey.pulseId}',
        conversationType: ConversationType.pulse,
        symmetricKey: groupKey.keyData,
        createdAt: groupKey.createdAt,
      );

      final ciphertext = base64Decode(encryptedContent);
      final encryptedData = EncryptedData(
        ciphertext: ciphertext,
        metadata: EncryptionMetadata(
          keyId: groupKey.keyId,
          algorithm: 'aes-256-gcm',
          iv: Uint8List(12), // Placeholder IV
        ),
      );

      final decryptedData = await _encryptionService.decryptData(
        encryptedData,
        conversationKey,
      );
      return utf8.decode(decryptedData);
    } catch (e) {
      debugPrint('❌ Error decrypting content: $e');
      return encryptedContent; // Return encrypted content as fallback
    }
  }

  /// Encrypt media data
  Future<Map<String, dynamic>> _encryptMediaData(
      MediaData mediaData, GroupEncryptionKey groupKey) async {
    try {
      // For now, return media data as-is
      // TODO: Implement media encryption when needed
      return mediaData.toJson();
    } catch (e) {
      debugPrint('❌ Error encrypting media data: $e');
      return mediaData.toJson();
    }
  }

  /// Decrypt media data
  Future<MediaData> _decryptMediaData(
      MediaData mediaData, GroupEncryptionKey groupKey) async {
    try {
      // For now, return media data as-is
      // TODO: Implement media decryption when needed
      return mediaData;
    } catch (e) {
      debugPrint('❌ Error decrypting media data: $e');
      return mediaData;
    }
  }

  /// Encrypt location data
  Future<Map<String, dynamic>> _encryptLocationData(
      LocationData locationData, GroupEncryptionKey groupKey) async {
    try {
      // Create a ConversationKey for encryption
      final conversationKey = ConversationKey(
        keyId: groupKey.keyId,
        conversationId: 'pulse_${groupKey.pulseId}',
        conversationType: ConversationType.pulse,
        symmetricKey: groupKey.keyData,
        createdAt: groupKey.createdAt,
      );

      final locationJson = locationData.toJson();
      final encryptedData = await _encryptionService.encryptData(
        Uint8List.fromList(utf8.encode(jsonEncode(locationJson))),
        conversationKey,
      );
      return {
        'encrypted_data': base64Encode(encryptedData.ciphertext),
        'is_encrypted': true,
      };
    } catch (e) {
      debugPrint('❌ Error encrypting location data: $e');
      return locationData.toJson();
    }
  }

  /// Decrypt location data
  Future<LocationData> _decryptLocationData(
      LocationData locationData, GroupEncryptionKey groupKey) async {
    try {
      // Check if location data is encrypted
      final locationJson = locationData.toJson();
      if (locationJson['is_encrypted'] == true &&
          locationJson['encrypted_data'] != null) {
        // Create a ConversationKey for decryption
        final conversationKey = ConversationKey(
          keyId: groupKey.keyId,
          conversationId: 'pulse_${groupKey.pulseId}',
          conversationType: ConversationType.pulse,
          symmetricKey: groupKey.keyData,
          createdAt: groupKey.createdAt,
        );

        final ciphertext = base64Decode(locationJson['encrypted_data']);
        final encryptedData = EncryptedData(
          ciphertext: ciphertext,
          metadata: EncryptionMetadata(
            keyId: groupKey.keyId,
            algorithm: 'aes-256-gcm',
            iv: Uint8List(12), // Placeholder IV
          ),
        );

        final decryptedData = await _encryptionService.decryptData(
          encryptedData,
          conversationKey,
        );
        final decryptedJson = jsonDecode(utf8.decode(decryptedData));
        return LocationData.fromJson(decryptedJson);
      }

      return locationData;
    } catch (e) {
      debugPrint('❌ Error decrypting location data: $e');
      return locationData;
    }
  }

  /// Clear cached keys
  void clearCache() {
    _groupKeyCache.clear();
  }

  /// Dispose of the service
  void dispose() {
    clearCache();
  }
}

/// Group encryption key model
class GroupEncryptionKey {
  final String keyId;
  final String pulseId;
  final Uint8List keyData;
  final int version;
  final DateTime createdAt;
  final bool isActive;

  GroupEncryptionKey({
    required this.keyId,
    required this.pulseId,
    required this.keyData,
    required this.version,
    required this.createdAt,
    required this.isActive,
  });

  Map<String, dynamic> toJson() => {
        'key_id': keyId,
        'pulse_id': pulseId,
        'key_data': base64Encode(keyData),
        'version': version,
        'created_at': createdAt.toIso8601String(),
        'is_active': isActive,
      };

  factory GroupEncryptionKey.fromJson(Map<String, dynamic> json) {
    return GroupEncryptionKey(
      keyId: json['key_id'],
      pulseId: json['pulse_id'],
      keyData: base64Decode(json['key_data']),
      version: json['version'],
      createdAt: DateTime.parse(json['created_at']),
      isActive: json['is_active'] ?? true,
    );
  }
}
