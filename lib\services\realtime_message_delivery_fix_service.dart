import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:pulsemeet/services/conversation_service.dart';
import 'package:pulsemeet/services/automatic_conversation_service.dart';
import 'package:pulsemeet/models/message.dart';

/// Service to fix real-time message delivery issues specifically for pulse groups
class RealtimeMessageDeliveryFixService {
  static final RealtimeMessageDeliveryFixService _instance = RealtimeMessageDeliveryFixService._internal();
  factory RealtimeMessageDeliveryFixService() => _instance;
  RealtimeMessageDeliveryFixService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final ConversationService _conversationService = ConversationService();
  final AutomaticConversationService _autoConversationService = AutomaticConversationService();
  
  // Track active real-time channels
  final Map<String, RealtimeChannel> _activeChannels = {};
  final Map<String, StreamController<Message>> _messageStreams = {};

  /// Fix real-time message delivery for a specific pulse group
  Future<bool> fixRealtimeDeliveryForPulseGroup(String pulseId) async {
    try {
      debugPrint('🔧 Fixing real-time message delivery for pulse: $pulseId');

      // Step 1: Get the conversation
      final conversation = await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for pulse: $pulseId');
        return false;
      }

      debugPrint('✅ Found conversation: ${conversation.id}');

      // Step 2: Ensure ConversationService is initialized and subscribed
      await _conversationService.ensureInitialized();
      await _conversationService.subscribeToMessages(conversation.id);
      
      // Step 3: Set up direct real-time channel for immediate delivery
      await _setupDirectRealtimeChannel(conversation.id, pulseId);
      
      // Step 4: Test the real-time delivery
      final testResult = await _testRealtimeDelivery(conversation.id);
      
      if (testResult) {
        debugPrint('✅ Real-time message delivery fixed for pulse: $pulseId');
        return true;
      } else {
        debugPrint('❌ Real-time message delivery test failed for pulse: $pulseId');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error fixing real-time delivery: $e');
      return false;
    }
  }

  /// Set up direct real-time channel for immediate message delivery
  Future<void> _setupDirectRealtimeChannel(String conversationId, String pulseId) async {
    try {
      debugPrint('📡 Setting up direct real-time channel for conversation: $conversationId');

      // Clean up existing channel if any
      await _cleanupChannel(conversationId);

      // Create new channel
      final channelName = 'direct_messages_$conversationId';
      final channel = _supabase.channel(channelName);

      // Create message stream
      _messageStreams[conversationId] = StreamController<Message>.broadcast();

      // Listen for message insertions
      channel.on(
        RealtimeListenTypes.postgresChanges,
        ChannelFilter(
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: 'conversation_id=eq.$conversationId',
        ),
        (payload, [ref]) {
          debugPrint('📨 Direct real-time message received: ${payload['new']?['id']}');
          _handleDirectMessageInsert(payload, conversationId);
        },
      );

      // Listen for message updates
      channel.on(
        RealtimeListenTypes.postgresChanges,
        ChannelFilter(
          event: 'UPDATE',
          schema: 'public',
          table: 'messages',
          filter: 'conversation_id=eq.$conversationId',
        ),
        (payload, [ref]) {
          debugPrint('📝 Direct real-time message update: ${payload['new']?['id']}');
          _handleDirectMessageUpdate(payload, conversationId);
        },
      );

      // Subscribe to the channel
      channel.subscribe((status, [error]) {
        if (status == 'SUBSCRIBED') {
          debugPrint('✅ Direct real-time channel subscribed: $channelName');
        } else if (status == 'CHANNEL_ERROR') {
          debugPrint('❌ Direct real-time channel error: $error');
        } else {
          debugPrint('📡 Channel status: $status');
        }
      });

      _activeChannels[conversationId] = channel;
      debugPrint('✅ Direct real-time channel setup complete');
    } catch (e) {
      debugPrint('❌ Error setting up direct real-time channel: $e');
    }
  }

  /// Handle direct message insert from real-time
  Future<void> _handleDirectMessageInsert(Map<String, dynamic> payload, String conversationId) async {
    try {
      final messageData = payload['new'] as Map<String, dynamic>?;
      if (messageData == null) return;

      debugPrint('📨 Processing direct message insert: ${messageData['id']}');
      debugPrint('📨 Message content: ${messageData['content']}');
      debugPrint('📨 Sender: ${messageData['sender_id']}');

      // Create message object
      final message = Message.fromJson(messageData);
      
      // Emit to stream for immediate UI update
      _messageStreams[conversationId]?.add(message);
      
      // Also trigger ConversationService update for consistency
      _conversationService.updateLastMessageCache(conversationId, message);
      
      debugPrint('✅ Direct message processed and delivered: ${message.id}');
    } catch (e) {
      debugPrint('❌ Error handling direct message insert: $e');
    }
  }

  /// Handle direct message update from real-time
  Future<void> _handleDirectMessageUpdate(Map<String, dynamic> payload, String conversationId) async {
    try {
      final messageData = payload['new'] as Map<String, dynamic>?;
      if (messageData == null) return;

      debugPrint('📝 Processing direct message update: ${messageData['id']}');

      // Create message object
      final message = Message.fromJson(messageData);
      
      // Emit to stream for immediate UI update
      _messageStreams[conversationId]?.add(message);
      
      debugPrint('✅ Direct message update processed: ${message.id}');
    } catch (e) {
      debugPrint('❌ Error handling direct message update: $e');
    }
  }

  /// Test real-time delivery by sending a test message
  Future<bool> _testRealtimeDelivery(String conversationId) async {
    try {
      debugPrint('🧪 Testing real-time delivery for conversation: $conversationId');

      // Set up a listener for the test message
      final completer = Completer<bool>();
      bool messageReceived = false;
      
      final testMessageContent = 'Real-time delivery test ${DateTime.now().millisecondsSinceEpoch}';
      
      // Listen for the test message
      final subscription = _messageStreams[conversationId]?.stream.listen((message) {
        if (message.content.contains('Real-time delivery test') && !messageReceived) {
          messageReceived = true;
          debugPrint('✅ Test message received via real-time: ${message.id}');
          if (!completer.isCompleted) {
            completer.complete(true);
          }
        }
      });

      // Send test message
      final sentMessage = await _conversationService.sendTextMessage(conversationId, testMessageContent);
      
      if (sentMessage == null) {
        debugPrint('❌ Failed to send test message');
        subscription?.cancel();
        return false;
      }

      debugPrint('📤 Test message sent: ${sentMessage.id}');

      // Wait for real-time delivery (with timeout)
      final result = await completer.future.timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          debugPrint('⏰ Real-time delivery test timed out');
          return false;
        },
      );

      subscription?.cancel();
      
      if (result) {
        debugPrint('✅ Real-time delivery test passed');
      } else {
        debugPrint('❌ Real-time delivery test failed');
      }

      return result;
    } catch (e) {
      debugPrint('❌ Error testing real-time delivery: $e');
      return false;
    }
  }

  /// Get message stream for a conversation
  Stream<Message>? getMessageStream(String conversationId) {
    return _messageStreams[conversationId]?.stream;
  }

  /// Force refresh real-time subscriptions for all active conversations
  Future<void> forceRefreshAllSubscriptions() async {
    try {
      debugPrint('🔄 Force refreshing all real-time subscriptions');
      
      final conversationIds = _activeChannels.keys.toList();
      
      for (final conversationId in conversationIds) {
        debugPrint('🔄 Refreshing subscription for: $conversationId');
        
        // Get pulse ID from conversation
        final conversationResponse = await _supabase
            .from('conversations')
            .select('pulse_id')
            .eq('id', conversationId)
            .maybeSingle();
            
        if (conversationResponse != null && conversationResponse['pulse_id'] != null) {
          final pulseId = conversationResponse['pulse_id'] as String;
          await _setupDirectRealtimeChannel(conversationId, pulseId);
        }
      }
      
      debugPrint('✅ All real-time subscriptions refreshed');
    } catch (e) {
      debugPrint('❌ Error refreshing subscriptions: $e');
    }
  }

  /// Clean up channel for a conversation
  Future<void> _cleanupChannel(String conversationId) async {
    try {
      final channel = _activeChannels[conversationId];
      if (channel != null) {
        await channel.unsubscribe();
        _activeChannels.remove(conversationId);
      }
      
      _messageStreams[conversationId]?.close();
      _messageStreams.remove(conversationId);
      
      debugPrint('🧹 Cleaned up channel for conversation: $conversationId');
    } catch (e) {
      debugPrint('❌ Error cleaning up channel: $e');
    }
  }

  /// Dispose of all channels and streams
  void dispose() {
    final conversationIds = _activeChannels.keys.toList();
    for (final conversationId in conversationIds) {
      _cleanupChannel(conversationId);
    }
  }

  /// Get diagnostic information about active channels
  Map<String, dynamic> getDiagnosticInfo() {
    return {
      'active_channels': _activeChannels.length,
      'active_streams': _messageStreams.length,
      'conversation_ids': _activeChannels.keys.toList(),
    };
  }
}
