import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:pulsemeet/services/enhanced_encryption_service.dart';

/// Service for sharing and managing live location data
class LocationSharingService {
  static final LocationSharingService _instance = LocationSharingService._internal();
  factory LocationSharingService() => _instance;
  LocationSharingService._internal();

  final EnhancedEncryptionService _encryptionService = EnhancedEncryptionService();
  
  StreamSubscription<Position>? _locationSubscription;
  final Map<String, LiveLocationSession> _activeSessions = {};
  
  // Stream controllers for location updates
  final StreamController<LocationUpdate> _locationUpdateController = 
      StreamController<LocationUpdate>.broadcast();

  Stream<LocationUpdate> get locationUpdates => _locationUpdateController.stream;

  /// Get current location with high accuracy
  Future<LocationData?> getCurrentLocation() async {
    try {
      final hasPermission = await _checkLocationPermission();
      if (!hasPermission) {
        debugPrint('❌ Location permission denied');
        return null;
      }

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      final locationData = LocationData(
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
        timestamp: DateTime.now(),
        address: await _getAddressFromCoordinates(position.latitude, position.longitude),
      );

      debugPrint('📍 Current location: ${locationData.latitude}, ${locationData.longitude}');
      return locationData;
    } catch (e) {
      debugPrint('❌ Error getting current location: $e');
      return null;
    }
  }

  /// Start sharing live location for a specific duration
  Future<String?> startLiveLocationSharing({
    required String conversationId,
    required Duration duration,
    LocationAccuracy accuracy = LocationAccuracy.high,
  }) async {
    try {
      final hasPermission = await _checkLocationPermission();
      if (!hasPermission) {
        debugPrint('❌ Location permission denied for live sharing');
        return null;
      }

      final sessionId = DateTime.now().millisecondsSinceEpoch.toString();
      final session = LiveLocationSession(
        sessionId: sessionId,
        conversationId: conversationId,
        startTime: DateTime.now(),
        endTime: DateTime.now().add(duration),
        accuracy: accuracy,
      );

      _activeSessions[sessionId] = session;

      // Start location stream
      _locationSubscription = Geolocator.getPositionStream(
        locationSettings: LocationSettings(
          accuracy: accuracy,
          distanceFilter: 10, // Update every 10 meters
          timeLimit: const Duration(seconds: 30),
        ),
      ).listen(
        (position) => _handleLocationUpdate(sessionId, position),
        onError: (error) => debugPrint('❌ Location stream error: $error'),
      );

      // Auto-stop after duration
      Timer(duration, () => stopLiveLocationSharing(sessionId));

      debugPrint('📍 Started live location sharing: $sessionId for ${duration.inMinutes} minutes');
      return sessionId;
    } catch (e) {
      debugPrint('❌ Error starting live location sharing: $e');
      return null;
    }
  }

  /// Stop live location sharing
  Future<void> stopLiveLocationSharing(String sessionId) async {
    try {
      final session = _activeSessions[sessionId];
      if (session != null) {
        await _locationSubscription?.cancel();
        _activeSessions.remove(sessionId);
        
        // Notify that session ended
        _locationUpdateController.add(LocationUpdate(
          sessionId: sessionId,
          conversationId: session.conversationId,
          locationData: null,
          isSessionEnded: true,
        ));

        debugPrint('📍 Stopped live location sharing: $sessionId');
      }
    } catch (e) {
      debugPrint('❌ Error stopping live location sharing: $e');
    }
  }

  /// Stop all active location sharing sessions
  Future<void> stopAllLocationSharing() async {
    final sessionIds = List<String>.from(_activeSessions.keys);
    for (final sessionId in sessionIds) {
      await stopLiveLocationSharing(sessionId);
    }
  }

  /// Handle location updates from the stream
  void _handleLocationUpdate(String sessionId, Position position) {
    final session = _activeSessions[sessionId];
    if (session == null) return;

    // Check if session is still active
    if (DateTime.now().isAfter(session.endTime)) {
      stopLiveLocationSharing(sessionId);
      return;
    }

    final locationData = LocationData(
      latitude: position.latitude,
      longitude: position.longitude,
      accuracy: position.accuracy,
      timestamp: DateTime.now(),
    );

    // Encrypt location data before sharing
    _encryptAndShareLocation(sessionId, session.conversationId, locationData);
  }

  /// Encrypt and share location data
  Future<void> _encryptAndShareLocation(
    String sessionId,
    String conversationId,
    LocationData locationData,
  ) async {
    try {
      // Encrypt the location data
      final encryptedData = await _encryptLocationData(locationData);

      // Create location update
      final update = LocationUpdate(
        sessionId: sessionId,
        conversationId: conversationId,
        locationData: locationData,
        encryptedData: encryptedData,
        isSessionEnded: false,
      );

      _locationUpdateController.add(update);
    } catch (e) {
      debugPrint('❌ Error encrypting and sharing location: $e');
    }
  }

  /// Encrypt location data
  Future<Map<String, dynamic>> _encryptLocationData(LocationData locationData) async {
    try {
      final plainData = locationData.toJson();
      final result = await _encryptionService.encryptMessage(
        plainData.toString(),
        'location_sharing',
      );
      
      return {
        'encrypted_content': result.encryptedContent,
        'encryption_metadata': result.metadata,
      };
    } catch (e) {
      debugPrint('❌ Error encrypting location data: $e');
      return locationData.toJson(); // Return unencrypted as fallback
    }
  }

  /// Decrypt location data
  Future<LocationData?> decryptLocationData(Map<String, dynamic> encryptedData) async {
    try {
      final decryptedContent = await _encryptionService.decryptMessage(
        encryptedData['encrypted_content'],
        encryptedData['encryption_metadata'],
      );
      
      final locationJson = Map<String, dynamic>.from(decryptedContent);
      return LocationData.fromJson(locationJson);
    } catch (e) {
      debugPrint('❌ Error decrypting location data: $e');
      return null;
    }
  }

  /// Check location permission
  Future<bool> _checkLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }
    
    if (permission == LocationPermission.deniedForever) {
      debugPrint('❌ Location permission permanently denied');
      return false;
    }
    
    return permission == LocationPermission.whileInUse || 
           permission == LocationPermission.always;
  }

  /// Get address from coordinates (simplified implementation)
  Future<String?> _getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      // In a real implementation, you'd use a geocoding service
      // For now, return a formatted coordinate string
      return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
    } catch (e) {
      debugPrint('❌ Error getting address: $e');
      return null;
    }
  }

  /// Calculate distance between two locations
  double calculateDistance(LocationData from, LocationData to) {
    return Geolocator.distanceBetween(
      from.latitude,
      from.longitude,
      to.latitude,
      to.longitude,
    );
  }

  /// Get active sessions for a conversation
  List<LiveLocationSession> getActiveSessionsForConversation(String conversationId) {
    return _activeSessions.values
        .where((session) => session.conversationId == conversationId)
        .toList();
  }

  /// Dispose of the service
  Future<void> dispose() async {
    await stopAllLocationSharing();
    await _locationUpdateController.close();
  }

  // Getters
  bool get hasActiveSessions => _activeSessions.isNotEmpty;
  int get activeSessionCount => _activeSessions.length;
}

/// Data class for location information
class LocationData {
  final double latitude;
  final double longitude;
  final double accuracy;
  final DateTime timestamp;
  final String? address;

  LocationData({
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    required this.timestamp,
    this.address,
  });

  LatLng get latLng => LatLng(latitude, longitude);

  Map<String, dynamic> toJson() => {
    'latitude': latitude,
    'longitude': longitude,
    'accuracy': accuracy,
    'timestamp': timestamp.toIso8601String(),
    'address': address,
  };

  factory LocationData.fromJson(Map<String, dynamic> json) {
    return LocationData(
      latitude: json['latitude'] as double,
      longitude: json['longitude'] as double,
      accuracy: json['accuracy'] as double,
      timestamp: DateTime.parse(json['timestamp'] as String),
      address: json['address'] as String?,
    );
  }

  String get formattedCoordinates => 
      '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';

  String get formattedAccuracy => '±${accuracy.toStringAsFixed(0)}m';
}

/// Live location sharing session
class LiveLocationSession {
  final String sessionId;
  final String conversationId;
  final DateTime startTime;
  final DateTime endTime;
  final LocationAccuracy accuracy;

  LiveLocationSession({
    required this.sessionId,
    required this.conversationId,
    required this.startTime,
    required this.endTime,
    required this.accuracy,
  });

  Duration get duration => endTime.difference(startTime);
  Duration get remainingTime {
    final now = DateTime.now();
    if (now.isAfter(endTime)) return Duration.zero;
    return endTime.difference(now);
  }

  bool get isActive => DateTime.now().isBefore(endTime);
  double get progress {
    final total = duration.inMilliseconds;
    final elapsed = DateTime.now().difference(startTime).inMilliseconds;
    return (elapsed / total).clamp(0.0, 1.0);
  }
}

/// Location update event
class LocationUpdate {
  final String sessionId;
  final String conversationId;
  final LocationData? locationData;
  final Map<String, dynamic>? encryptedData;
  final bool isSessionEnded;

  LocationUpdate({
    required this.sessionId,
    required this.conversationId,
    this.locationData,
    this.encryptedData,
    required this.isSessionEnded,
  });
}
