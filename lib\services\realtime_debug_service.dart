import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Debug service to help identify real-time message delivery issues
class RealtimeDebugService {
  static final RealtimeDebugService _instance = RealtimeDebugService._internal();
  factory RealtimeDebugService() => _instance;
  RealtimeDebugService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final Map<String, RealtimeChannel> _debugChannels = {};
  final Map<String, List<String>> _receivedMessages = {};

  /// Start debugging real-time delivery for a conversation
  Future<void> startDebugging(String conversationId) async {
    try {
      debugPrint('🐛 Starting real-time debugging for conversation: $conversationId');

      // Clean up existing debug channel
      await _stopDebugging(conversationId);

      // Initialize received messages list
      _receivedMessages[conversationId] = [];

      // Create debug channel
      final channelName = 'debug_$conversationId';
      final channel = _supabase.channel(channelName);

      // Listen for all message events
      channel.on(
        RealtimeListenTypes.postgresChanges,
        ChannelFilter(
          event: '*',
          schema: 'public',
          table: 'messages',
          filter: 'conversation_id=eq.$conversationId',
        ),
        (payload, [ref]) {
          _handleDebugEvent(payload, conversationId);
        },
      );

      // Subscribe with detailed logging
      channel.subscribe((status, [error]) {
        debugPrint('🐛 Debug channel status: $status');
        if (error != null) {
          debugPrint('🐛 Debug channel error: $error');
        }
      });

      _debugChannels[conversationId] = channel;
      debugPrint('✅ Real-time debugging started for conversation: $conversationId');
    } catch (e) {
      debugPrint('❌ Error starting real-time debugging: $e');
    }
  }

  /// Handle debug events
  void _handleDebugEvent(Map<String, dynamic> payload, String conversationId) {
    try {
      final eventType = payload['eventType'] as String?;
      final messageData = payload['new'] as Map<String, dynamic>?;
      final oldData = payload['old'] as Map<String, dynamic>?;

      debugPrint('🐛 ===== REAL-TIME DEBUG EVENT =====');
      debugPrint('🐛 Conversation: $conversationId');
      debugPrint('🐛 Event Type: $eventType');
      debugPrint('🐛 Timestamp: ${DateTime.now().toIso8601String()}');

      if (messageData != null) {
        final messageId = messageData['id'] as String?;
        final content = messageData['content'] as String?;
        final senderId = messageData['sender_id'] as String?;
        final status = messageData['status'] as String?;
        final createdAt = messageData['created_at'] as String?;

        debugPrint('🐛 Message ID: $messageId');
        debugPrint('🐛 Content: $content');
        debugPrint('🐛 Sender ID: $senderId');
        debugPrint('🐛 Status: $status');
        debugPrint('🐛 Created At: $createdAt');

        // Track received messages
        if (messageId != null) {
          _receivedMessages[conversationId]?.add(messageId);
          debugPrint('🐛 Total messages received: ${_receivedMessages[conversationId]?.length}');
        }
      }

      if (oldData != null && eventType == 'UPDATE') {
        debugPrint('🐛 Old Data: $oldData');
      }

      debugPrint('🐛 ================================');
    } catch (e) {
      debugPrint('❌ Error handling debug event: $e');
    }
  }

  /// Stop debugging for a conversation
  Future<void> _stopDebugging(String conversationId) async {
    try {
      final channel = _debugChannels[conversationId];
      if (channel != null) {
        await channel.unsubscribe();
        _debugChannels.remove(conversationId);
      }
      _receivedMessages.remove(conversationId);
      debugPrint('🧹 Stopped debugging for conversation: $conversationId');
    } catch (e) {
      debugPrint('❌ Error stopping debugging: $e');
    }
  }

  /// Send a test message and track its delivery
  Future<String?> sendTestMessage(String conversationId) async {
    try {
      debugPrint('🧪 Sending test message for debugging...');

      final testContent = 'DEBUG TEST MESSAGE - ${DateTime.now().millisecondsSinceEpoch}';
      final currentUserId = _supabase.auth.currentUser?.id;

      if (currentUserId == null) {
        debugPrint('❌ No current user for test message');
        return null;
      }

      // Insert test message directly
      final insertedMessage = await _supabase.from('messages').insert({
        'conversation_id': conversationId,
        'sender_id': currentUserId,
        'content': testContent,
        'message_type': 'text',
        'status': 'sent',
      }).select().single();

      final messageId = insertedMessage['id'] as String;
      debugPrint('✅ Test message sent: $messageId');
      debugPrint('📤 Test message content: $testContent');

      return messageId;
    } catch (e) {
      debugPrint('❌ Error sending test message: $e');
      return null;
    }
  }

  /// Check if a message was received via real-time
  bool wasMessageReceived(String conversationId, String messageId) {
    return _receivedMessages[conversationId]?.contains(messageId) ?? false;
  }

  /// Get debug statistics
  Map<String, dynamic> getDebugStats(String conversationId) {
    return {
      'conversation_id': conversationId,
      'is_debugging': _debugChannels.containsKey(conversationId),
      'messages_received': _receivedMessages[conversationId]?.length ?? 0,
      'received_message_ids': _receivedMessages[conversationId] ?? [],
    };
  }

  /// Run comprehensive real-time test
  Future<bool> runComprehensiveTest(String conversationId) async {
    try {
      debugPrint('🧪 Running comprehensive real-time test for: $conversationId');

      // Start debugging
      await startDebugging(conversationId);

      // Wait a moment for subscription to be ready
      await Future.delayed(const Duration(seconds: 2));

      // Send test message
      final messageId = await sendTestMessage(conversationId);
      if (messageId == null) {
        debugPrint('❌ Failed to send test message');
        return false;
      }

      // Wait for real-time delivery
      debugPrint('⏳ Waiting for real-time delivery...');
      await Future.delayed(const Duration(seconds: 5));

      // Check if message was received
      final wasReceived = wasMessageReceived(conversationId, messageId);
      
      if (wasReceived) {
        debugPrint('✅ Real-time delivery test PASSED');
      } else {
        debugPrint('❌ Real-time delivery test FAILED');
        debugPrint('🐛 Debug stats: ${getDebugStats(conversationId)}');
      }

      return wasReceived;
    } catch (e) {
      debugPrint('❌ Error running comprehensive test: $e');
      return false;
    }
  }

  /// Check Supabase real-time connection status
  Future<Map<String, dynamic>> checkRealtimeStatus() async {
    try {
      debugPrint('🔍 Checking Supabase real-time status...');

      final status = {
        'client_connected': _supabase.realtime.isConnected,
        'active_channels': _supabase.realtime.channels.length,
        'debug_channels': _debugChannels.length,
      };

      debugPrint('📊 Real-time status: $status');
      return status;
    } catch (e) {
      debugPrint('❌ Error checking real-time status: $e');
      return {'error': e.toString()};
    }
  }

  /// Force reconnect real-time
  Future<void> forceReconnect() async {
    try {
      debugPrint('🔄 Forcing real-time reconnection...');
      
      // Disconnect and reconnect
      _supabase.realtime.disconnect();
      await Future.delayed(const Duration(seconds: 1));
      _supabase.realtime.connect();
      
      debugPrint('✅ Real-time reconnection completed');
    } catch (e) {
      debugPrint('❌ Error forcing reconnection: $e');
    }
  }

  /// Generate debug report
  String generateDebugReport(String conversationId) {
    final stats = getDebugStats(conversationId);
    final buffer = StringBuffer();
    
    buffer.writeln('🐛 REAL-TIME DEBUG REPORT');
    buffer.writeln('========================');
    buffer.writeln('Conversation ID: $conversationId');
    buffer.writeln('Is Debugging: ${stats['is_debugging']}');
    buffer.writeln('Messages Received: ${stats['messages_received']}');
    buffer.writeln('Client Connected: ${_supabase.realtime.isConnected}');
    buffer.writeln('Active Channels: ${_supabase.realtime.channels.length}');
    buffer.writeln('Debug Channels: ${_debugChannels.length}');
    buffer.writeln('');
    buffer.writeln('Received Message IDs:');
    for (final messageId in stats['received_message_ids']) {
      buffer.writeln('  - $messageId');
    }
    
    return buffer.toString();
  }

  /// Dispose of all debug channels
  void dispose() {
    final conversationIds = _debugChannels.keys.toList();
    for (final conversationId in conversationIds) {
      _stopDebugging(conversationId);
    }
  }
}
