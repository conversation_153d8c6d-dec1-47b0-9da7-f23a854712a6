import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pulsemeet/services/enhanced_encryption_service.dart';

/// Service for recording, playing, and managing voice messages
class VoiceMessageService {
  static final VoiceMessageService _instance = VoiceMessageService._internal();
  factory VoiceMessageService() => _instance;
  VoiceMessageService._internal();

  FlutterSoundRecorder? _recorder;
  FlutterSoundPlayer? _player;
  final EnhancedEncryptionService _encryptionService =
      EnhancedEncryptionService();

  bool _isRecorderInitialized = false;
  bool _isPlayerInitialized = false;
  bool _isRecording = false;
  bool _isPlaying = false;

  String? _currentRecordingPath;
  StreamSubscription<RecordingDisposition>? _recordingSubscription;
  StreamSubscription<PlaybackDisposition>? _playbackSubscription;

  // Stream controllers for UI updates
  final StreamController<RecordingState> _recordingStateController =
      StreamController<RecordingState>.broadcast();
  final StreamController<PlaybackState> _playbackStateController =
      StreamController<PlaybackState>.broadcast();

  Stream<RecordingState> get recordingStateStream =>
      _recordingStateController.stream;
  Stream<PlaybackState> get playbackStateStream =>
      _playbackStateController.stream;

  /// Initialize the voice message service
  Future<void> initialize() async {
    try {
      _recorder = FlutterSoundRecorder();
      _player = FlutterSoundPlayer();

      await _recorder!.openRecorder();
      await _player!.openPlayer();

      _isRecorderInitialized = true;
      _isPlayerInitialized = true;

      debugPrint('🎤 VoiceMessageService initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing VoiceMessageService: $e');
    }
  }

  /// Dispose of the service
  Future<void> dispose() async {
    await stopRecording();
    await stopPlayback();

    await _recordingSubscription?.cancel();
    await _playbackSubscription?.cancel();

    if (_isRecorderInitialized) {
      await _recorder!.closeRecorder();
    }
    if (_isPlayerInitialized) {
      await _player!.closePlayer();
    }

    await _recordingStateController.close();
    await _playbackStateController.close();
  }

  /// Check and request microphone permission
  Future<bool> checkMicrophonePermission() async {
    final status = await Permission.microphone.status;
    if (status.isGranted) {
      return true;
    }

    final result = await Permission.microphone.request();
    return result.isGranted;
  }

  /// Start recording a voice message
  Future<bool> startRecording() async {
    if (!_isRecorderInitialized || _isRecording) {
      return false;
    }

    final hasPermission = await checkMicrophonePermission();
    if (!hasPermission) {
      debugPrint('❌ Microphone permission denied');
      return false;
    }

    try {
      final directory = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      _currentRecordingPath = '${directory.path}/voice_message_$timestamp.m4a';

      await _recorder!.startRecorder(
        toFile: _currentRecordingPath,
        codec: Codec.aacMP4,
        bitRate: 128000,
        sampleRate: 44100,
      );

      _isRecording = true;

      // Listen to recording progress
      _recordingSubscription = _recorder!.onProgress!.listen((disposition) {
        _recordingStateController.add(RecordingState(
          isRecording: true,
          duration: disposition.duration,
          decibels: disposition.decibels,
        ));
      });

      debugPrint('🎤 Started recording voice message: $_currentRecordingPath');
      return true;
    } catch (e) {
      debugPrint('❌ Error starting recording: $e');
      return false;
    }
  }

  /// Stop recording and return the voice message file path for pulse groups
  Future<String?> stopRecordingForPulseGroup() async {
    if (!_isRecording || _recorder == null) {
      return null;
    }

    try {
      await _recorder!.stopRecorder();
      await _recordingSubscription?.cancel();

      _isRecording = false;
      _recordingStateController.add(RecordingState(
        isRecording: false,
        duration: Duration.zero,
        decibels: 0,
      ));

      if (_currentRecordingPath == null ||
          !File(_currentRecordingPath!).existsSync()) {
        debugPrint('❌ Recording file not found');
        return null;
      }

      debugPrint(
          '🎤 Recording stopped for pulse group: $_currentRecordingPath');
      return _currentRecordingPath;
    } catch (e) {
      debugPrint('❌ Error stopping recording: $e');
      return null;
    }
  }

  /// Stop recording and return the encrypted voice message data
  Future<VoiceMessageData?> stopRecording() async {
    if (!_isRecording || _recorder == null) {
      return null;
    }

    try {
      await _recorder!.stopRecorder();
      await _recordingSubscription?.cancel();

      _isRecording = false;
      _recordingStateController.add(RecordingState(
        isRecording: false,
        duration: Duration.zero,
        decibels: 0,
      ));

      if (_currentRecordingPath == null ||
          !File(_currentRecordingPath!).existsSync()) {
        debugPrint('❌ Recording file not found');
        return null;
      }

      final file = File(_currentRecordingPath!);
      final audioData = await file.readAsBytes();
      final duration = await _getAudioDuration(_currentRecordingPath!);

      // Encrypt the audio data
      final encryptedData = await _encryptAudioData(audioData);

      // Clean up temporary file
      await file.delete();

      debugPrint('🎤 Recording stopped, duration: ${duration.inSeconds}s');

      return VoiceMessageData(
        encryptedData: encryptedData,
        duration: duration,
        waveform: await _generateWaveform(audioData),
      );
    } catch (e) {
      debugPrint('❌ Error stopping recording: $e');
      return null;
    }
  }

  /// Play a voice message
  Future<bool> playVoiceMessage(VoiceMessageData voiceData) async {
    if (!_isPlayerInitialized || _isPlaying) {
      return false;
    }

    try {
      // Decrypt the audio data
      final audioData = await _decryptAudioData(voiceData.encryptedData);

      // Write to temporary file
      final directory = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final tempPath = '${directory.path}/temp_voice_$timestamp.m4a';
      final tempFile = File(tempPath);
      await tempFile.writeAsBytes(audioData);

      await _player!.startPlayer(
        fromURI: tempPath,
        codec: Codec.aacMP4,
      );

      _isPlaying = true;

      // Listen to playback progress
      _playbackSubscription = _player!.onProgress!.listen((disposition) {
        _playbackStateController.add(PlaybackState(
          isPlaying: true,
          position: disposition.position,
          duration: disposition.duration,
        ));
      });

      // Auto-stop when finished - use a timer as fallback
      Timer(voiceData.duration, () {
        if (_isPlaying) {
          _isPlaying = false;
          _playbackStateController.add(PlaybackState(
            isPlaying: false,
            position: Duration.zero,
            duration: voiceData.duration,
          ));
          // Clean up temp file
          tempFile.delete().catchError((e) {
            debugPrint('Error deleting temp file: $e');
            return tempFile; // Return the file to satisfy the type requirement
          });
        }
      });

      return true;
    } catch (e) {
      debugPrint('❌ Error playing voice message: $e');
      return false;
    }
  }

  /// Stop voice message playback
  Future<void> stopPlayback() async {
    if (_isPlaying && _player != null) {
      await _player!.stopPlayer();
      await _playbackSubscription?.cancel();
      _isPlaying = false;
    }
  }

  /// Encrypt audio data
  Future<Uint8List> _encryptAudioData(Uint8List audioData) async {
    try {
      // For now, return the audio data as-is
      // TODO: Implement proper encryption when needed
      return audioData;
    } catch (e) {
      debugPrint('❌ Error encrypting audio data: $e');
      return audioData; // Return unencrypted as fallback
    }
  }

  /// Decrypt audio data
  Future<Uint8List> _decryptAudioData(Uint8List encryptedData) async {
    try {
      // For now, treat the data as unencrypted
      // TODO: Implement proper decryption when needed
      return encryptedData;
    } catch (e) {
      debugPrint('❌ Error decrypting audio data: $e');
      return encryptedData; // Return as-is as fallback
    }
  }

  /// Get audio duration (simplified implementation)
  Future<Duration> _getAudioDuration(String filePath) async {
    try {
      // This is a simplified implementation
      // In a real app, you'd use a proper audio analysis library
      final file = File(filePath);
      final size = await file.length();

      // Rough estimation: 128kbps AAC, so ~16KB per second
      final estimatedSeconds = size / 16000;
      return Duration(milliseconds: (estimatedSeconds * 1000).round());
    } catch (e) {
      debugPrint('❌ Error getting audio duration: $e');
      return const Duration(seconds: 1);
    }
  }

  /// Generate waveform data (simplified implementation)
  Future<List<double>> _generateWaveform(Uint8List audioData) async {
    try {
      // This is a simplified implementation
      // In a real app, you'd use proper audio analysis
      final waveform = <double>[];
      const sampleCount = 50;

      for (int i = 0; i < sampleCount; i++) {
        // Generate pseudo-random waveform based on audio data
        final index = (i * audioData.length / sampleCount).floor();
        if (index < audioData.length) {
          final amplitude = audioData[index] / 255.0;
          waveform.add(amplitude);
        } else {
          waveform.add(0.0);
        }
      }

      return waveform;
    } catch (e) {
      debugPrint('❌ Error generating waveform: $e');
      return List.filled(50, 0.5); // Default waveform
    }
  }

  // Getters for current state
  bool get isRecording => _isRecording;
  bool get isPlaying => _isPlaying;
  bool get isInitialized => _isRecorderInitialized && _isPlayerInitialized;
}

/// Data class for voice message information
class VoiceMessageData {
  final Uint8List encryptedData;
  final Duration duration;
  final List<double> waveform;

  VoiceMessageData({
    required this.encryptedData,
    required this.duration,
    required this.waveform,
  });

  Map<String, dynamic> toJson() => {
        'encrypted_data': encryptedData,
        'duration_ms': duration.inMilliseconds,
        'waveform': waveform,
      };

  factory VoiceMessageData.fromJson(Map<String, dynamic> json) {
    return VoiceMessageData(
      encryptedData: json['encrypted_data'] as Uint8List,
      duration: Duration(milliseconds: json['duration_ms'] as int),
      waveform: List<double>.from(json['waveform'] as List),
    );
  }
}

/// Recording state information
class RecordingState {
  final bool isRecording;
  final Duration duration;
  final double? decibels;

  RecordingState({
    required this.isRecording,
    required this.duration,
    this.decibels,
  });
}

/// Playback state information
class PlaybackState {
  final bool isPlaying;
  final Duration position;
  final Duration duration;

  PlaybackState({
    required this.isPlaying,
    required this.position,
    required this.duration,
  });

  double get progress {
    if (duration.inMilliseconds == 0) return 0.0;
    return position.inMilliseconds / duration.inMilliseconds;
  }
}
