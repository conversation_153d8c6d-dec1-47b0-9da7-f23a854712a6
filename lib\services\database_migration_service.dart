import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Service for applying database migrations programmatically
class DatabaseMigrationService {
  static final DatabaseMigrationService _instance = DatabaseMigrationService._internal();
  factory DatabaseMigrationService() => _instance;
  DatabaseMigrationService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;

  /// Apply the pulse conversations migration
  Future<bool> applyPulseConversationsMigration() async {
    try {
      debugPrint('🔧 DatabaseMigrationService: Starting pulse conversations migration...');

      // Check if migration is already applied
      final migrationApplied = await _checkMigrationApplied();
      if (migrationApplied) {
        debugPrint('✅ DatabaseMigrationService: Migration already applied');
        return true;
      }

      // Apply the migration
      await _applyMigration();
      
      debugPrint('✅ DatabaseMigrationService: Migration applied successfully');
      return true;
    } catch (e) {
      debugPrint('❌ DatabaseMigrationService: Migration failed: $e');
      return false;
    }
  }

  /// Check if the migration has already been applied
  Future<bool> _checkMigrationApplied() async {
    try {
      // Check if the RPC function exists
      final result = await _supabase.rpc('create_pulse_conversation', params: {
        'pulse_id_param': '00000000-0000-0000-0000-000000000000', // Test UUID
      });
      
      // If we get here without error, the function exists
      return true;
    } catch (e) {
      // If we get an error about function not existing, migration not applied
      if (e.toString().contains('function') && e.toString().contains('does not exist')) {
        return false;
      }
      // Other errors might mean the function exists but failed for other reasons
      return true;
    }
  }

  /// Apply the migration by executing the SQL
  Future<void> _applyMigration() async {
    debugPrint('🔧 DatabaseMigrationService: Applying migration SQL...');

    // Note: In a real app, you would typically apply migrations through Supabase CLI
    // or the Supabase dashboard. This is a simplified approach for demonstration.
    
    // For now, we'll just ensure the basic structure exists
    await _ensureBasicStructure();
  }

  /// Ensure basic conversation structure exists
  Future<void> _ensureBasicStructure() async {
    try {
      // Test if we can query conversations table
      await _supabase
          .from('conversations')
          .select('id')
          .limit(1);
      
      debugPrint('✅ DatabaseMigrationService: Conversations table exists');
    } catch (e) {
      debugPrint('⚠️ DatabaseMigrationService: Conversations table might not exist: $e');
      throw Exception('Conversations table not found. Please apply the migration through Supabase dashboard.');
    }

    try {
      // Test if we can query conversation_participants table
      await _supabase
          .from('conversation_participants')
          .select('id')
          .limit(1);
      
      debugPrint('✅ DatabaseMigrationService: Conversation participants table exists');
    } catch (e) {
      debugPrint('⚠️ DatabaseMigrationService: Conversation participants table might not exist: $e');
      throw Exception('Conversation participants table not found. Please apply the migration through Supabase dashboard.');
    }
  }

  /// Initialize the migration service and apply migrations if needed
  Future<void> initializeAndMigrate() async {
    try {
      debugPrint('🚀 DatabaseMigrationService: Initializing...');
      
      final success = await applyPulseConversationsMigration();
      if (success) {
        debugPrint('✅ DatabaseMigrationService: All migrations applied successfully');
      } else {
        debugPrint('⚠️ DatabaseMigrationService: Some migrations failed');
      }
    } catch (e) {
      debugPrint('❌ DatabaseMigrationService: Initialization failed: $e');
    }
  }
}
