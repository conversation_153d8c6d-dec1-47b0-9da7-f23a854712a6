-- Complete setup for pulse group conversations
-- This migration creates all necessary tables, RLS policies, and functions for automatic pulse chat creation

-- 1. Ensure conversations table exists with proper structure
CREATE TABLE IF NOT EXISTS conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(20) NOT NULL CHECK (type IN ('pulse_group')),
    title VARCHAR(255),
    description TEXT,
    avatar_url TEXT,
    pulse_id UUID REFERENCES pulses(id) ON DELETE CASCADE,
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE,
    is_archived BOOLEAN NOT NULL DEFAULT false,
    is_muted BOOLEAN NOT NULL DEFAULT false,
    settings JSONB NOT NULL DEFAULT '{}',
    encryption_enabled BOOLEAN NOT NULL DEFAULT true,
    encryption_key_id VARCHAR(255),
    
    -- Ensure unique conversation per pulse
    UNIQUE(pulse_id, type)
);

-- 2. Ensure conversation_participants table exists
CREATE TABLE IF NOT EXISTS conversation_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL DEFAULT 'member' CHECK (role IN ('admin', 'moderator', 'member')),
    joined_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    last_read_at TIMESTAMP WITH TIME ZONE,
    is_muted BOOLEAN NOT NULL DEFAULT false,
    notification_settings JSONB NOT NULL DEFAULT '{}',
    
    -- Ensure unique participant per conversation
    UNIQUE(conversation_id, user_id)
);

-- 3. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversations_pulse_id ON conversations(pulse_id);
CREATE INDEX IF NOT EXISTS idx_conversations_type ON conversations(type);
CREATE INDEX IF NOT EXISTS idx_conversations_created_by ON conversations(created_by);
CREATE INDEX IF NOT EXISTS idx_conversations_last_message_at ON conversations(last_message_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversation_participants_conversation_id ON conversation_participants(conversation_id);
CREATE INDEX IF NOT EXISTS idx_conversation_participants_user_id ON conversation_participants(user_id);

-- 4. Enable RLS on both tables
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversation_participants ENABLE ROW LEVEL SECURITY;

-- 5. Create RLS policies for conversations table
DROP POLICY IF EXISTS "Users can view conversations they participate in" ON conversations;
CREATE POLICY "Users can view conversations they participate in"
ON conversations FOR SELECT
TO authenticated
USING (
    -- User is a participant in the conversation
    EXISTS (
        SELECT 1 FROM conversation_participants cp
        WHERE cp.conversation_id = conversations.id
        AND cp.user_id = auth.uid()
    ) OR
    -- User is the creator of the conversation
    created_by = auth.uid() OR
    -- For pulse conversations, user is a pulse participant
    (type = 'pulse_group' AND pulse_id IS NOT NULL AND EXISTS (
        SELECT 1 FROM pulse_participants pp
        WHERE pp.pulse_id = conversations.pulse_id
        AND pp.user_id = auth.uid()
        AND pp.status = 'active'
    ))
);

DROP POLICY IF EXISTS "Service can create conversations" ON conversations;
CREATE POLICY "Service can create conversations"
ON conversations FOR INSERT
TO authenticated
WITH CHECK (
    -- User can create conversations for pulses they participate in
    (type = 'pulse_group' AND pulse_id IS NOT NULL AND (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM pulse_participants pp
            WHERE pp.pulse_id = conversations.pulse_id
            AND pp.user_id = auth.uid()
            AND pp.status = 'active'
        )
    ))
);

DROP POLICY IF EXISTS "Users can update their conversations" ON conversations;
CREATE POLICY "Users can update their conversations"
ON conversations FOR UPDATE
TO authenticated
USING (
    created_by = auth.uid() OR
    EXISTS (
        SELECT 1 FROM conversation_participants cp
        WHERE cp.conversation_id = conversations.id
        AND cp.user_id = auth.uid()
        AND cp.role IN ('admin', 'moderator')
    )
);

-- 6. Create RLS policies for conversation_participants table
DROP POLICY IF EXISTS "Users can view conversation participants" ON conversation_participants;
CREATE POLICY "Users can view conversation participants"
ON conversation_participants FOR SELECT
TO authenticated
USING (
    -- User is viewing their own participation
    user_id = auth.uid() OR
    -- User is a participant in the same conversation
    EXISTS (
        SELECT 1 FROM conversation_participants cp2
        WHERE cp2.conversation_id = conversation_participants.conversation_id
        AND cp2.user_id = auth.uid()
    )
);

DROP POLICY IF EXISTS "Users can join conversations" ON conversation_participants;
CREATE POLICY "Users can join conversations"
ON conversation_participants FOR INSERT
TO authenticated
WITH CHECK (
    -- User can add themselves to conversations for pulses they participate in
    user_id = auth.uid() AND
    EXISTS (
        SELECT 1 FROM conversations c
        LEFT JOIN pulse_participants pp ON c.pulse_id = pp.pulse_id
        WHERE c.id = conversation_participants.conversation_id
        AND (
            c.created_by = auth.uid() OR
            (pp.user_id = auth.uid() AND pp.status = 'active')
        )
    )
);

DROP POLICY IF EXISTS "Users can update their participation" ON conversation_participants;
CREATE POLICY "Users can update their participation"
ON conversation_participants FOR UPDATE
TO authenticated
USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM conversation_participants cp2
        WHERE cp2.conversation_id = conversation_participants.conversation_id
        AND cp2.user_id = auth.uid()
        AND cp2.role IN ('admin', 'moderator')
    )
);

-- 7. Create function to automatically create pulse conversation
CREATE OR REPLACE FUNCTION create_pulse_conversation(pulse_id_param UUID)
RETURNS UUID AS $$
DECLARE
    conversation_id_val UUID;
    pulse_title TEXT;
    pulse_creator UUID;
BEGIN
    -- Get pulse information
    SELECT title, creator_id INTO pulse_title, pulse_creator
    FROM pulses WHERE id = pulse_id_param;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Pulse not found: %', pulse_id_param;
    END IF;
    
    -- Check if conversation already exists
    SELECT id INTO conversation_id_val
    FROM conversations
    WHERE pulse_id = pulse_id_param AND type = 'pulse_group';
    
    IF FOUND THEN
        -- Return existing conversation ID
        RETURN conversation_id_val;
    END IF;
    
    -- Create new conversation
    INSERT INTO conversations (
        id,
        type,
        title,
        pulse_id,
        created_by,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid(),
        'pulse_group',
        pulse_title || ' Chat',
        pulse_id_param,
        pulse_creator,
        NOW(),
        NOW()
    ) RETURNING id INTO conversation_id_val;
    
    -- Add pulse creator as admin participant
    INSERT INTO conversation_participants (
        conversation_id,
        user_id,
        role,
        joined_at
    ) VALUES (
        conversation_id_val,
        pulse_creator,
        'admin',
        NOW()
    ) ON CONFLICT (conversation_id, user_id) DO NOTHING;
    
    RETURN conversation_id_val;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Create function to add user to pulse conversation
CREATE OR REPLACE FUNCTION add_user_to_pulse_conversation(pulse_id_param UUID, user_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
    conversation_id_val UUID;
BEGIN
    -- Get conversation ID for the pulse
    SELECT id INTO conversation_id_val
    FROM conversations
    WHERE pulse_id = pulse_id_param AND type = 'pulse_group';
    
    IF NOT FOUND THEN
        -- Create conversation if it doesn't exist
        conversation_id_val := create_pulse_conversation(pulse_id_param);
    END IF;
    
    -- Add user as participant
    INSERT INTO conversation_participants (
        conversation_id,
        user_id,
        role,
        joined_at
    ) VALUES (
        conversation_id_val,
        user_id_param,
        'member',
        NOW()
    ) ON CONFLICT (conversation_id, user_id) DO NOTHING;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Create trigger to automatically create conversation when pulse is created
CREATE OR REPLACE FUNCTION trigger_create_pulse_conversation()
RETURNS TRIGGER AS $$
BEGIN
    -- Create conversation for the new pulse
    PERFORM create_pulse_conversation(NEW.id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS auto_create_pulse_conversation ON pulses;
CREATE TRIGGER auto_create_pulse_conversation
    AFTER INSERT ON pulses
    FOR EACH ROW
    EXECUTE FUNCTION trigger_create_pulse_conversation();

-- 10. Create trigger to automatically add users to conversation when they join pulse
CREATE OR REPLACE FUNCTION trigger_add_user_to_pulse_conversation()
RETURNS TRIGGER AS $$
BEGIN
    -- Only add to conversation if user is active participant
    IF NEW.status = 'active' THEN
        PERFORM add_user_to_pulse_conversation(NEW.pulse_id, NEW.user_id);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS auto_add_user_to_pulse_conversation ON pulse_participants;
CREATE TRIGGER auto_add_user_to_pulse_conversation
    AFTER INSERT OR UPDATE ON pulse_participants
    FOR EACH ROW
    EXECUTE FUNCTION trigger_add_user_to_pulse_conversation();

-- 11. Grant necessary permissions
GRANT EXECUTE ON FUNCTION create_pulse_conversation(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION add_user_to_pulse_conversation(UUID, UUID) TO authenticated;
