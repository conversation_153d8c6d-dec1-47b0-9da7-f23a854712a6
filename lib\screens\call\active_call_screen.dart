import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';

import 'package:pulsemeet/models/call.dart';
import 'package:pulsemeet/services/call_manager_service.dart';
import 'package:pulsemeet/services/ringtone_service.dart';
import 'package:pulsemeet/widgets/avatar.dart';

/// Screen for active calls (voice and video)
class ActiveCallScreen extends StatefulWidget {
  final Call call;

  const ActiveCallScreen({
    super.key,
    required this.call,
  });

  @override
  State<ActiveCallScreen> createState() => _ActiveCallScreenState();
}

class _ActiveCallScreenState extends State<ActiveCallScreen> {
  final CallManagerService _callManager = CallManagerService();
  final RingtoneService _ringtoneService = RingtoneService();

  // Call state
  Call? _currentCall;
  MediaStream? _localStream;
  MediaStream? _remoteStream;

  // UI state
  bool _isMuted = false;
  bool _isSpeakerOn = false;
  bool _isCameraOn = true;
  bool _isControlsVisible = true;
  bool _isMinimized = false;

  // Call duration
  Timer? _durationTimer;
  Duration _callDuration = Duration.zero;

  // Stream subscriptions
  StreamSubscription<Call?>? _callStateSubscription;
  StreamSubscription<MediaStream?>? _localStreamSubscription;
  StreamSubscription<MediaStream?>? _remoteStreamSubscription;

  // Video renderers
  final RTCVideoRenderer _localRenderer = RTCVideoRenderer();
  final RTCVideoRenderer _remoteRenderer = RTCVideoRenderer();

  @override
  void initState() {
    super.initState();
    _currentCall = widget.call;
    _initializeRenderers();
    _initializeRingtone();
    _setupStreamSubscriptions();
    _startCallDurationTimer();
    _handleInitialCallState();
  }

  /// Initialize ringtone service
  Future<void> _initializeRingtone() async {
    try {
      await _ringtoneService.initialize();
      debugPrint('✅ Ringtone service initialized for active call');
    } catch (e) {
      debugPrint('❌ Error initializing ringtone service: $e');
    }
  }

  /// Handle initial call state for outgoing calls
  void _handleInitialCallState() {
    if (_currentCall?.state == CallState.connecting &&
        _currentCall?.callerId == _currentCall?.callerId) {
      // This is an outgoing call, start dial tone
      _ringtoneService.startOutgoingCallTone();
    }
  }

  @override
  void dispose() {
    _durationTimer?.cancel();
    _callStateSubscription?.cancel();
    _localStreamSubscription?.cancel();
    _remoteStreamSubscription?.cancel();
    _localRenderer.dispose();
    _remoteRenderer.dispose();
    super.dispose();
  }

  Future<void> _initializeRenderers() async {
    await _localRenderer.initialize();
    await _remoteRenderer.initialize();
  }

  void _setupStreamSubscriptions() {
    // Listen for call state changes
    _callStateSubscription = _callManager.callStateStream.listen((call) {
      if (mounted) {
        final previousState = _currentCall?.state;
        setState(() {
          _currentCall = call;
        });

        // Handle call state changes for audio feedback
        _handleCallStateChange(previousState, call?.state);

        // Handle call end
        if (call == null || call.isEnded) {
          _handleCallEnd();
        }
      }
    });

    // Listen for local stream
    _localStreamSubscription = _callManager.localStreamStream.listen((stream) {
      if (mounted) {
        setState(() {
          _localStream = stream;
        });

        if (stream != null) {
          _localRenderer.srcObject = stream;
        }
      }
    });

    // Listen for remote stream
    _remoteStreamSubscription =
        _callManager.remoteStreamStream.listen((stream) {
      if (mounted) {
        setState(() {
          _remoteStream = stream;
        });

        if (stream != null) {
          _remoteRenderer.srcObject = stream;
        }
      }
    });
  }

  void _startCallDurationTimer() {
    _durationTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted && _currentCall?.state == CallState.connected) {
        setState(() {
          _callDuration = Duration(seconds: _callDuration.inSeconds + 1);
        });
      }
    });
  }

  /// Handle call state changes for audio feedback
  void _handleCallStateChange(CallState? previousState, CallState? newState) {
    if (newState == null) return;

    switch (newState) {
      case CallState.connected:
        // Stop any ringing when call connects
        _ringtoneService.stopAllSounds();
        // Play connection sound
        _ringtoneService.playConnectionSound();
        break;
      case CallState.ended:
      case CallState.declined:
      case CallState.missed:
      case CallState.failed:
        // Stop all sounds and play end tone
        _ringtoneService.stopAllSounds();
        _ringtoneService.playCallEndedSound();
        break;
      default:
        break;
    }
  }

  void _handleCallEnd() {
    // Stop all ringtones
    _ringtoneService.stopAllSounds();
    Navigator.of(context).pop();
  }

  Future<void> _endCall() async {
    await _callManager.endCall();
  }

  Future<void> _toggleMute() async {
    await _callManager.toggleMicrophone();
    setState(() {
      _isMuted = !_isMuted;
    });
  }

  Future<void> _toggleSpeaker() async {
    await _callManager.toggleSpeaker();
    setState(() {
      _isSpeakerOn = !_isSpeakerOn;
    });
  }

  Future<void> _toggleCamera() async {
    if (_currentCall?.type == CallType.video) {
      await _callManager.toggleCamera();
      setState(() {
        _isCameraOn = !_isCameraOn;
      });
    }
  }

  Future<void> _switchCamera() async {
    if (_currentCall?.type == CallType.video) {
      await _callManager.switchCamera();
    }
  }

  void _toggleControls() {
    setState(() {
      _isControlsVisible = !_isControlsVisible;
    });
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    if (_currentCall == null) {
      return const Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: CircularProgressIndicator(
            color: Colors.white,
          ),
        ),
      );
    }

    // Get other participant info with safe fallback
    CallParticipant? otherParticipant;

    if (_currentCall!.participants.isNotEmpty) {
      try {
        // Try to find the other participant (not the caller)
        otherParticipant = _currentCall!.participants.firstWhere(
          (p) => p.userId != _currentCall!.callerId,
          orElse: () => _currentCall!.participants.first,
        );
      } catch (e) {
        debugPrint('⚠️ Error finding other participant: $e');
        // Fallback to first participant if available
        otherParticipant = _currentCall!.participants.isNotEmpty
            ? _currentCall!.participants.first
            : null;
      }
    }

    // If no participant found, create a fallback participant
    if (otherParticipant == null) {
      final otherUserId =
          _currentCall!.getOtherParticipantId(_currentCall!.callerId);
      otherParticipant = CallParticipant(
        userId: otherUserId,
        name: 'Unknown User',
        joinedAt: DateTime.now(),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _toggleControls,
        child: Stack(
          children: [
            // Video content or avatar background
            if (_currentCall!.type == CallType.video)
              _buildVideoContent()
            else
              _buildVoiceCallContent(otherParticipant),

            // Controls overlay
            if (_isControlsVisible) _buildControlsOverlay(otherParticipant),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoContent() {
    return Stack(
      children: [
        // Remote video (full screen)
        if (_remoteStream != null)
          Positioned.fill(
            child: RTCVideoView(
              _remoteRenderer,
              objectFit: RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,
            ),
          )
        else
          Container(
            color: Colors.grey[900],
            child: const Center(
              child: Text(
                'Connecting...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ),
          ),

        // Local video (picture-in-picture)
        if (_localStream != null && _isCameraOn)
          Positioned(
            top: 60,
            right: 20,
            child: GestureDetector(
              onTap: _switchCamera,
              child: Container(
                width: 120,
                height: 160,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 2,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: RTCVideoView(
                    _localRenderer,
                    objectFit: RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,
                    mirror: true,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildVoiceCallContent(CallParticipant participant) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.blue.shade900,
            Colors.black,
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Avatar
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 4,
                ),
              ),
              child: ClipOval(
                child: UserAvatar(
                  userId: participant.userId,
                  displayName: participant.name,
                  avatarUrl: participant.avatarUrl,
                  size: 192,
                ),
              ),
            ),

            const SizedBox(height: 32),

            // Name
            Text(
              participant.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 28,
                fontWeight: FontWeight.w300,
              ),
            ),

            const SizedBox(height: 8),

            // Call status
            Text(
              _currentCall?.state == CallState.connected
                  ? _formatDuration(_callDuration)
                  : _getCallStatusText(),
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlsOverlay(CallParticipant participant) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.7),
            Colors.transparent,
            Colors.transparent,
            Colors.black.withOpacity(0.7),
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Top bar
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  // Back button (minimize)
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),

                  const Spacer(),

                  // Call info
                  Column(
                    children: [
                      Text(
                        participant.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        _currentCall?.state == CallState.connected
                            ? _formatDuration(_callDuration)
                            : _getCallStatusText(),
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),

                  const Spacer(),

                  // Settings button
                  IconButton(
                    onPressed: () {
                      // TODO: Show call settings
                    },
                    icon: const Icon(
                      Icons.more_vert,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),

            const Spacer(),

            // Bottom controls
            Padding(
              padding: const EdgeInsets.all(40),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Mute button
                  _buildControlButton(
                    icon: _isMuted ? Icons.mic_off : Icons.mic,
                    isActive: _isMuted,
                    onTap: _toggleMute,
                  ),

                  // Speaker button (voice calls only)
                  if (_currentCall?.type == CallType.voice)
                    _buildControlButton(
                      icon: _isSpeakerOn ? Icons.volume_up : Icons.volume_down,
                      isActive: _isSpeakerOn,
                      onTap: _toggleSpeaker,
                    ),

                  // Camera button (video calls only)
                  if (_currentCall?.type == CallType.video)
                    _buildControlButton(
                      icon: _isCameraOn ? Icons.videocam : Icons.videocam_off,
                      isActive: !_isCameraOn,
                      onTap: _toggleCamera,
                    ),

                  // End call button
                  _buildControlButton(
                    icon: Icons.call_end,
                    isActive: false,
                    backgroundColor: Colors.red,
                    onTap: _endCall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required bool isActive,
    Color? backgroundColor,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: backgroundColor ??
              (isActive ? Colors.white : Colors.white.withOpacity(0.2)),
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: backgroundColor != null
              ? Colors.white
              : (isActive ? Colors.black : Colors.white),
          size: 28,
        ),
      ),
    );
  }

  String _getCallStatusText() {
    if (_currentCall == null) return 'Initializing...';

    switch (_currentCall!.state) {
      case CallState.connecting:
        return 'Connecting...';
      case CallState.ringing:
        return 'Ringing...';
      case CallState.connected:
        return 'Connected';
      default:
        return '';
    }
  }
}
