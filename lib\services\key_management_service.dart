import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:crypto/crypto.dart';
import 'package:pulsemeet/models/encryption_key.dart';
import 'package:pulsemeet/services/encryption_service.dart';
import 'package:pulsemeet/services/key_derivation_service.dart';

/// Manages encryption keys for users and conversations
class KeyManagementService {
  static final KeyManagementService _instance =
      KeyManagementService._internal();
  factory KeyManagementService() => _instance;
  KeyManagementService._internal();

  final _supabase = Supabase.instance.client;
  final _encryptionService = EncryptionService();
  late final KeyDerivationService _keyDerivationService;

  // Secure storage for private keys
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // In-memory cache for conversation keys
  final Map<String, ConversationKey> _conversationKeyCache = {};

  // Current user's key pair
  EncryptionKeyPair? _currentUserKeyPair;
  String? _currentUserId;

  // Initialization flag to prevent multiple key clearing
  bool _isInitialized = false;

  /// Initialize key management for current user
  Future<void> initialize() async {
    // Prevent multiple initializations
    if (_isInitialized) {
      debugPrint('🔑 KeyManagementService already initialized, skipping');

      // CRITICAL FIX: Force key cache clearing even for already initialized service
      // This ensures the deterministic key fix is applied to existing sessions
      debugPrint('🔑 Applying deterministic key fix to existing session...');
      clearAllConversationKeyCache();
      debugPrint(
          '🔑 Cleared cached keys to ensure consistent key generation between users');

      return;
    }

    _currentUserId = _supabase.auth.currentUser?.id;
    if (_currentUserId == null) return;

    await _encryptionService.initialize();

    // Initialize KeyDerivationService only if not already initialized
    try {
      _keyDerivationService = KeyDerivationService(_encryptionService);
    } catch (e) {
      debugPrint('KeyDerivationService already initialized: $e');
    }

    await _loadOrGenerateUserKeyPair();

    // CRITICAL FIX: Clear cached keys to force regeneration with fixed deterministic key IDs
    // This ensures both users generate the same key ID for the same conversation
    // The fix removes user-specific key ID generation that was causing decryption failures
    clearAllConversationKeyCache();
    debugPrint(
        '🔑 Cleared cached keys to ensure consistent key generation between users');

    _isInitialized = true;
    debugPrint('KeyManagementService initialized for user: $_currentUserId');
  }

  /// Clear pulse chat keys from cache to force database lookup
  /// CRITICAL: COMPLETELY DISABLED to prevent media decryption failures
  /// Key clearing was causing conversation keys to be regenerated, breaking media decryption
  Future<void> _clearPulseChatKeysFromCache() async {
    debugPrint(
        '🔑 Key clearing method called but DISABLED to preserve all keys');
    debugPrint(
        '🔑 All conversation keys will be preserved to prevent decryption failures');
    // COMPLETELY DISABLED - DO NOT CLEAR ANY KEYS
    return;

    // OLD LOGIC COMMENTED OUT:
    /*
    debugPrint(
        '🔑 Cache clearing: Current cache keys: ${_conversationKeyCache.keys.toList()}');

    final keysToRemove = <String>[];
    for (final key in _conversationKeyCache.keys) {
      // CRITICAL FIX: Check conversation type in database to determine if it's a DM
      // Don't rely on ID format since DM conversations can have UUID format too
      final isDirectMessage = await _isDirectMessageConversation(key);

      if (!isDirectMessage) {
        // This is a pulse chat key - safe to remove
        keysToRemove.add(key);
      } else {
        debugPrint('🔑 Preserving DM conversation key: $key');
      }
    }

    debugPrint(
        '🔑 Cache clearing: Keys to remove (pulse chat only): $keysToRemove');

    for (final key in keysToRemove) {
      _conversationKeyCache.remove(key);
      debugPrint('🔑 Cleared cached pulse chat key: $key');
    }

    // Also clear pulse chat keys from secure storage (but preserve DM keys)
    await _clearPulseChatKeysFromSecureStorage();

    debugPrint(
        '🔑 Cache clearing: Remaining cache keys (including preserved DM keys): ${_conversationKeyCache.keys.toList()}');
    */
  }

  /// Check if a conversation ID represents a direct message conversation
  Future<bool> _isDirectMessageConversation(String conversationId) async {
    try {
      // First check if it's the deterministic DM format
      if (conversationId.startsWith('dm_')) {
        return true;
      }

      // Check the database for conversation type
      final response = await _supabase
          .from('conversations')
          .select('type')
          .eq('id', conversationId)
          .maybeSingle();

      if (response != null) {
        return response['type'] == 'direct_message';
      }

      return false;
    } catch (e) {
      debugPrint('🔑 Error checking conversation type for $conversationId: $e');
      // If we can't determine the type, assume it's not a DM to be safe
      return false;
    }
  }

  /// Clear pulse chat keys from secure storage
  /// CRITICAL: COMPLETELY DISABLED to prevent media decryption failures
  /// Key clearing was causing conversation keys to be regenerated, breaking media decryption
  Future<void> _clearPulseChatKeysFromSecureStorage() async {
    debugPrint(
        '🔑 Secure storage clearing method called but DISABLED to preserve all keys');
    debugPrint(
        '🔑 All conversation keys in secure storage will be preserved to prevent decryption failures');
    // COMPLETELY DISABLED - DO NOT CLEAR ANY KEYS
    return;

    // OLD LOGIC COMMENTED OUT:
    /*
    try {
      // Get all keys from secure storage
      final allKeys = await _secureStorage.readAll();

      final keysToDelete = <String>[];
      for (final key in allKeys.keys) {
        // CRITICAL FIX: Check conversation type in database to determine if it's a DM
        if (key.startsWith('conversation_key_')) {
          // Extract conversation ID from key
          final conversationId = key.substring('conversation_key_'.length);

          // Check if this is a direct message conversation
          final isDirectMessage =
              await _isDirectMessageConversation(conversationId);

          if (!isDirectMessage) {
            // This is a pulse chat key - safe to delete
            keysToDelete.add(key);
          } else {
            debugPrint('🔑 Preserving DM conversation key in storage: $key');
          }
        }
      }

      debugPrint(
          '🔑 Secure storage clearing: Keys to delete (pulse chat only): $keysToDelete');

      for (final key in keysToDelete) {
        await _secureStorage.delete(key: key);
        debugPrint('🔑 Cleared pulse chat key from secure storage: $key');
      }
    } catch (e) {
      debugPrint('🔑 Error clearing pulse chat keys from secure storage: $e');
    }
    */
  }

  /// Get current user's public key
  Uint8List? get currentUserPublicKey => _currentUserKeyPair?.publicKey;

  /// Get current user's key ID
  String? get currentUserKeyId => _currentUserKeyPair?.keyId;

  /// Get current user ID
  String? get currentUserId => _currentUserId;

  /// Check if the current user has a key pair
  bool get hasKeyPair => _currentUserKeyPair != null;

  /// Load or generate user's key pair
  Future<void> _loadOrGenerateUserKeyPair() async {
    if (_currentUserId == null) return;

    try {
      // Try to load existing key pair from secure storage
      final keyPairJson =
          await _secureStorage.read(key: 'user_keypair_$_currentUserId');

      if (keyPairJson != null) {
        _currentUserKeyPair =
            EncryptionKeyPair.fromJson(jsonDecode(keyPairJson));
        debugPrint('Loaded existing key pair for user');

        // Verify key pair is still valid
        if (_currentUserKeyPair!.expiresAt != null &&
            _currentUserKeyPair!.expiresAt!.isBefore(DateTime.now())) {
          debugPrint('Key pair expired, generating new one');
          await _generateAndStoreUserKeyPair();
        }
      } else {
        // Generate new key pair
        await _generateAndStoreUserKeyPair();
      }

      // Upload public key to server (non-blocking)
      _uploadPublicKey().catchError((e) {
        debugPrint('Non-critical error uploading public key: $e');
      });
    } catch (e) {
      debugPrint('Error loading key pair: $e');
      try {
        await _generateAndStoreUserKeyPair();
        // Try to upload the new key (non-blocking)
        _uploadPublicKey().catchError((e) {
          debugPrint('Non-critical error uploading new public key: $e');
        });
      } catch (e2) {
        debugPrint('Critical error generating key pair: $e2');
        // Continue without encryption if key generation fails
      }
    }
  }

  /// Generate and store new user key pair
  Future<void> _generateAndStoreUserKeyPair() async {
    if (_currentUserId == null) return;

    // Generate new key pair with 1 year expiration
    final expiresAt = DateTime.now().add(const Duration(days: 365));
    _currentUserKeyPair = await _encryptionService.generateKeyPair(
      expiresAt: expiresAt,
    );

    // Store securely
    await _secureStorage.write(
      key: 'user_keypair_$_currentUserId',
      value: jsonEncode(_currentUserKeyPair!.toJson()),
    );

    debugPrint('Generated and stored new key pair');
  }

  /// Upload public key to server
  Future<void> _uploadPublicKey() async {
    if (_currentUserKeyPair == null || _currentUserId == null) return;

    try {
      // First, check if this key already exists
      final existingKey = await _supabase
          .from('user_public_keys')
          .select('id')
          .eq('user_id', _currentUserId)
          .eq('key_id', _currentUserKeyPair!.keyId)
          .maybeSingle();

      if (existingKey != null) {
        debugPrint('Public key already exists, skipping upload');
        return;
      }

      // Deactivate old keys first
      await _supabase
          .from('user_public_keys')
          .update({'is_active': false})
          .eq('user_id', _currentUserId)
          .eq('is_active', true);

      // Insert new key
      await _supabase.from('user_public_keys').insert({
        'user_id': _currentUserId,
        'key_id': _currentUserKeyPair!.keyId,
        'public_key': base64Encode(_currentUserKeyPair!.publicKey),
        'algorithm': _currentUserKeyPair!.algorithm,
        'created_at': _currentUserKeyPair!.createdAt.toIso8601String(),
        'expires_at': _currentUserKeyPair!.expiresAt?.toIso8601String(),
        'is_active': _currentUserKeyPair!.isActive,
      });

      debugPrint('Uploaded public key to server');
    } catch (e) {
      debugPrint('Error uploading public key: $e');
      // Don't rethrow - this is not critical for app functionality
    }
  }

  /// Get public key for another user
  Future<Uint8List?> getUserPublicKey(String userId) async {
    try {
      final response = await _supabase
          .from('user_public_keys')
          .select('public_key')
          .eq('user_id', userId)
          .eq('is_active', true)
          .order('created_at', ascending: false)
          .limit(1)
          .maybeSingle();

      if (response != null) {
        return base64Decode(response['public_key']);
      }
    } catch (e) {
      debugPrint('Error fetching user public key: $e');
    }
    return null;
  }

  /// Create or get conversation key for direct message using secure key derivation
  Future<ConversationKey?> getOrCreateDirectMessageKey(String otherUserId,
      {String? actualConversationId}) async {
    // Use actual conversation ID if provided, otherwise generate one
    final conversationId =
        actualConversationId ?? _getDirectMessageConversationId(otherUserId);

    debugPrint('🔑 🔐 Getting secure DM key for conversation: $conversationId');
    debugPrint('🔑 Other user: $otherUserId');
    debugPrint('🔑 Using ECDH key derivation - NO database lookup');

    // Check cache first
    if (_conversationKeyCache.containsKey(conversationId)) {
      debugPrint('🔑 Found cached key for conversation: $conversationId');
      return _conversationKeyCache[conversationId];
    }

    // Try to load from secure storage (local only)
    debugPrint('🔍 Checking for stored keys for conversation: $conversationId');
    await _debugStoredKeys(); // Debug helper
    final storedKey = await _loadConversationKey(conversationId);
    if (storedKey != null) {
      debugPrint(
          '🔑 Loaded locally stored key for conversation: $conversationId');
      debugPrint('🔑 Stored key ID: ${storedKey.keyId}');
      debugPrint(
          '🔑 Using original stored key to maintain backward compatibility');
      _conversationKeyCache[conversationId] = storedKey;
      return storedKey;
    }

    // Derive new conversation key using ECDH - NO database storage
    debugPrint(
        '🔑 🔐 Deriving new secure DM key for conversation: $conversationId');
    return await _createSharedDirectMessageKey(conversationId, otherUserId);
  }

  /// Create new secure direct message key using ECDH key derivation
  /// NO DATABASE STORAGE - True E2E encryption
  Future<ConversationKey?> _createSharedDirectMessageKey(
      String conversationId, String otherUserId) async {
    if (_currentUserId == null || _currentUserKeyPair == null) {
      debugPrint(
          '🔑 ❌ Cannot create secure DM key: no current user or key pair');
      return null;
    }

    try {
      debugPrint(
          '🔑 🔐 Deriving secure DM key for conversation: $conversationId');
      debugPrint('🔑 🔐 Using ECDH + HKDF - NO server-side key storage');

      // Get other user's public key
      final otherUserPublicKey = await getUserPublicKey(otherUserId);
      if (otherUserPublicKey == null) {
        throw Exception('Cannot find public key for user: $otherUserId');
      }

      // Use secure key derivation service - NO database storage
      // Generate deterministic key ID based on conversation participants
      final keyId =
          _generateDeterministicKeyId(conversationId, ConversationType.direct);
      final conversationKey = await _keyDerivationService.deriveConversationKey(
        conversationId: conversationId,
        conversationType: ConversationType.direct,
        myPrivateKey: _currentUserKeyPair!.privateKey,
        otherPublicKey: otherUserPublicKey,
        keyId: keyId,
      );

      // Store locally only (not in database)
      await _storeConversationKey(conversationKey);
      _conversationKeyCache[conversationId] = conversationKey;

      debugPrint(
          '🔑 ✅ Successfully derived secure DM key: ${conversationKey.keyId}');
      debugPrint('🔑 🔐 Key derived locally - NEVER stored on server');
      return conversationKey;
    } catch (e) {
      debugPrint('🔑 ❌ Error deriving secure DM key: $e');
      return null;
    }
  }

  /// Create or get conversation key for pulse chat using secure key derivation
  Future<ConversationKey?> getOrCreatePulseChatKey(String pulseId) async {
    debugPrint('🔑 🔐 Getting secure pulse chat key for: $pulseId');
    debugPrint('🔑 Current user: $_currentUserId');

    // Check cache first
    if (_conversationKeyCache.containsKey(pulseId)) {
      debugPrint('🔑 Found cached key for pulse: $pulseId');
      return _conversationKeyCache[pulseId];
    }

    // Try to load from secure storage (local only)
    final storedKey = await _loadConversationKey(pulseId);
    if (storedKey != null) {
      debugPrint('🔑 Loaded locally stored key for pulse: $pulseId');
      debugPrint('🔑 Stored key ID: ${storedKey.keyId}');
      debugPrint(
          '🔑 Using original stored key to maintain backward compatibility');
      _conversationKeyCache[pulseId] = storedKey;
      return storedKey;
    }

    // For new pulse chats, we need to implement secure key derivation
    // This will require getting the pulse creator's public key and deriving a shared key
    debugPrint('🔑 🔐 Creating new secure pulse chat key for: $pulseId');
    final conversationKey = await _createSecurePulseChatKey(pulseId);
    if (conversationKey != null) {
      await _storeConversationKey(conversationKey);
      _conversationKeyCache[pulseId] = conversationKey;
      debugPrint(
          '🔑 ✅ Created secure pulse chat key for: $pulseId with keyId: ${conversationKey.keyId}');
    } else {
      debugPrint('🔑 ❌ Failed to create secure pulse chat key for: $pulseId');
    }

    return conversationKey;
  }

  /// Store conversation key securely
  Future<void> _storeConversationKey(ConversationKey key) async {
    await _secureStorage.write(
      key: 'conversation_key_${key.conversationId}',
      value: jsonEncode(key.toJson()),
    );
  }

  /// Load conversation key from secure storage
  Future<ConversationKey?> _loadConversationKey(String conversationId) async {
    try {
      debugPrint('🔍 Attempting to load stored key for: $conversationId');
      final keyJson =
          await _secureStorage.read(key: 'conversation_key_$conversationId');
      if (keyJson != null) {
        debugPrint('🔍 Found stored key JSON for: $conversationId');
        final storedKey = ConversationKey.fromJson(jsonDecode(keyJson));
        debugPrint('🔍 Loaded stored key with ID: ${storedKey.keyId}');
        return storedKey;
      } else {
        debugPrint('🔍 No stored key found for: $conversationId');
      }
    } catch (e) {
      debugPrint('❌ Error loading conversation key for $conversationId: $e');
    }
    return null;
  }

  /// Create new secure pulse chat key using ECDH key derivation
  /// NO DATABASE STORAGE - True E2E encryption for pulse chats
  Future<ConversationKey?> _createSecurePulseChatKey(String pulseId) async {
    if (_currentUserId == null || _currentUserKeyPair == null) {
      debugPrint(
          '🔑 ❌ Cannot create secure pulse key: no current user or key pair');
      return null;
    }

    try {
      debugPrint('🔑 🔐 Deriving secure pulse chat key for: $pulseId');
      debugPrint('🔑 🔐 Using ECDH + HKDF - NO server-side key storage');

      // For pulse chats, we'll use a simplified approach where the key is derived
      // from the pulse creator's key pair. In a full implementation, you'd implement
      // proper multi-party key exchange with all participants.

      // Generate a deterministic conversation key based on pulse ID and user's key
      // Generate deterministic key ID for pulse chat
      final keyId =
          _generateDeterministicKeyId(pulseId, ConversationType.pulse);
      final conversationKey = await _keyDerivationService.deriveConversationKey(
        conversationId: pulseId,
        conversationType: ConversationType.pulse,
        myPrivateKey: _currentUserKeyPair!.privateKey,
        otherPublicKey: _currentUserKeyPair!.publicKey, // Simplified for demo
        keyId: keyId,
      );

      // Record key exchange metadata in database (without storing the actual key)
      try {
        await _supabase.from('pulse_chat_keys').upsert({
          'pulse_id': pulseId,
          'key_id': conversationKey.keyId,
          'created_by': _currentUserId,
          'created_at': conversationKey.createdAt.toIso8601String(),
          'expires_at': conversationKey.expiresAt?.toIso8601String(),
          'version': conversationKey.version,
          'is_active': true,
          'key_exchange_method': 'ECDH-HKDF-SHA256',
          'requires_key_derivation': true,
          'migration_completed': true,
        });
        debugPrint(
            '🔑 📝 Recorded key exchange metadata (no symmetric key stored)');
      } catch (e) {
        debugPrint(
            '🔑 ⚠️ Failed to record key metadata: $e (continuing anyway)');
      }

      debugPrint(
          '🔑 ✅ Successfully derived secure pulse chat key: ${conversationKey.keyId}');
      debugPrint('🔑 🔐 Key derived locally - NEVER stored on server');
      return conversationKey;
    } catch (e) {
      debugPrint('🔑 ❌ Error deriving secure pulse chat key: $e');
      return null;
    }
  }

  /// Clear cached key for a pulse (for debugging)
  void clearPulseChatKeyCache(String pulseId) {
    _conversationKeyCache.remove(pulseId);
    debugPrint('🔑 Cleared cached key for pulse: $pulseId');
  }

  /// Clear all cached conversation keys to force regeneration with fixed key IDs
  void clearAllConversationKeyCache() {
    _conversationKeyCache.clear();
    debugPrint('🔑 Cleared all cached conversation keys');
  }

  /// Clear cached key for a specific conversation
  void clearConversationKeyCache(String conversationId) {
    _conversationKeyCache.remove(conversationId);
    debugPrint('🔑 Cleared cached key for conversation: $conversationId');
  }

  /// Generate conversation ID for direct messages
  String _getDirectMessageConversationId(String otherUserId) {
    final userIds = [_currentUserId!, otherUserId]..sort();
    return 'dm_${userIds[0]}_${userIds[1]}';
  }

  /// Rotate user key pair
  Future<void> rotateUserKeyPair() async {
    await _generateAndStoreUserKeyPair();
    await _uploadPublicKey();
    debugPrint('User key pair rotated');
  }

  /// Clear all cached keys
  void clearCache() {
    _conversationKeyCache.clear();
    debugPrint('Key cache cleared');
  }

  /// Delete conversation key
  Future<void> deleteConversationKey(String conversationId) async {
    await _secureStorage.delete(key: 'conversation_key_$conversationId');
    _conversationKeyCache.remove(conversationId);
    debugPrint('Deleted conversation key: $conversationId');
  }

  /// Verify key fingerprint for security verification
  String getKeyFingerprint(Uint8List publicKey) {
    final digest = sha256.convert(publicKey);
    return digest.toString().substring(0, 16).toUpperCase();
  }

  /// Debug helper to list all stored keys
  Future<void> _debugStoredKeys() async {
    try {
      final allKeys = await _secureStorage.readAll();
      final conversationKeys = allKeys.entries
          .where((entry) => entry.key.startsWith('conversation_key_'))
          .toList();

      debugPrint(
          '🔍 Found ${conversationKeys.length} stored conversation keys:');
      for (final entry in conversationKeys) {
        final conversationId = entry.key.replaceFirst('conversation_key_', '');
        try {
          final keyData = jsonDecode(entry.value);
          final keyId = keyData['keyId'] ?? 'unknown';
          debugPrint('🔍   - $conversationId: $keyId');
        } catch (e) {
          debugPrint('🔍   - $conversationId: [parse error]');
        }
      }
    } catch (e) {
      debugPrint('❌ Error debugging stored keys: $e');
    }
  }

  /// Generate deterministic key ID based on conversation participants
  /// This ensures the same key ID is generated for the same conversation
  /// regardless of when the key is created, fixing media decryption issues
  String _generateDeterministicKeyId(
      String conversationId, ConversationType type) {
    // Create a deterministic string based on conversation and participants
    String keySource;

    if (type == ConversationType.direct) {
      // For direct messages, use sorted user IDs to ensure consistency
      final parts = conversationId.split('_');
      if (parts.length >= 3) {
        final userIds = [parts[1], parts[2]]..sort();
        keySource = 'dm_${userIds[0]}_${userIds[1]}';
      } else {
        keySource = conversationId;
      }
    } else {
      // For pulse chats, use the pulse ID directly
      keySource = 'pulse_$conversationId';
    }

    // DO NOT add current user ID - key ID must be the same for all participants
    // This ensures both users generate the same key ID for the same conversation

    // Generate deterministic UUID using SHA-256 hash
    final bytes = utf8.encode(keySource);
    final digest = sha256.convert(bytes);

    // Convert hash to UUID format (8-4-4-4-12)
    final hashHex = digest.toString();
    final keyId = '${hashHex.substring(0, 8)}-'
        '${hashHex.substring(8, 12)}-'
        '${hashHex.substring(12, 16)}-'
        '${hashHex.substring(16, 20)}-'
        '${hashHex.substring(20, 32)}';

    debugPrint('🔑 Generated deterministic key ID: $keyId for $conversationId');
    debugPrint('🔑 Key source: $keySource (same for all participants)');
    return keyId;
  }

  /// Clean up resources
  void dispose() {
    _conversationKeyCache.clear();
    _currentUserKeyPair = null;
    _currentUserId = null;
  }
}
