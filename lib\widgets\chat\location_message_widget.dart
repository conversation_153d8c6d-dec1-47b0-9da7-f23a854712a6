import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:pulsemeet/models/message.dart';

/// Widget for displaying location messages with map preview
class LocationMessageWidget extends StatefulWidget {
  final Message message;
  final bool isCurrentUser;

  const LocationMessageWidget({
    super.key,
    required this.message,
    required this.isCurrentUser,
  });

  @override
  State<LocationMessageWidget> createState() => _LocationMessageWidgetState();
}

class _LocationMessageWidgetState extends State<LocationMessageWidget> {
  LocationData? _locationData;
  bool _isLiveLocation = false;

  @override
  void initState() {
    super.initState();
    _loadLocationData();
  }

  void _loadLocationData() {
    // Use the locationData from the message if available
    if (widget.message.locationData != null) {
      setState(() {
        _locationData = widget.message.locationData;
        _isLiveLocation = widget.message.locationData!.isLiveLocation;
      });
    } else {
      // Fallback: try to parse from content
      _parseLocationFromContent();
    }
  }

  void _parseLocationFromContent() {
    try {
      // Try to parse coordinates from message content
      final content = widget.message.content;
      final regex = RegExp(r'(-?\d+\.?\d*),\s*(-?\d+\.?\d*)');
      final match = regex.firstMatch(content);

      if (match != null) {
        final lat = double.parse(match.group(1)!);
        final lng = double.parse(match.group(2)!);

        setState(() {
          _locationData = LocationData(
            latitude: lat,
            longitude: lng,
            address: content,
          );
          _isLiveLocation = false;
        });
      }
    } catch (e) {
      debugPrint('Error parsing location from content: $e');
    }
  }

  Future<void> _openInMaps() async {
    if (_locationData == null) return;

    final lat = _locationData!.latitude;
    final lng = _locationData!.longitude;

    // Try to open in Google Maps app first, then fallback to web
    final googleMapsUrl = 'comgooglemaps://?q=$lat,$lng';
    final webMapsUrl =
        'https://www.google.com/maps/search/?api=1&query=$lat,$lng';

    try {
      if (await canLaunchUrl(Uri.parse(googleMapsUrl))) {
        await launchUrl(Uri.parse(googleMapsUrl));
      } else {
        await launchUrl(Uri.parse(webMapsUrl),
            mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('Error opening maps: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    if (_locationData == null) {
      return _buildLoadingWidget();
    }

    return Container(
      constraints: const BoxConstraints(maxWidth: 280),
      decoration: BoxDecoration(
        color: widget.isCurrentUser
            ? theme.colorScheme.primary
            : (isDark ? const Color(0xFF2A2A2A) : Colors.grey[100]),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Map preview
          ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
            child: SizedBox(
              height: 150,
              child: _buildMapPreview(),
            ),
          ),

          // Location details
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Location type indicator
                Row(
                  children: [
                    Icon(
                      _isLiveLocation ? Icons.my_location : Icons.location_on,
                      size: 16,
                      color: widget.isCurrentUser
                          ? Colors.white
                          : theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _isLiveLocation ? 'Live Location' : 'Location',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: widget.isCurrentUser
                            ? Colors.white
                            : theme.colorScheme.primary,
                      ),
                    ),
                    if (_isLiveLocation) ...[
                      const SizedBox(width: 4),
                      Container(
                        width: 6,
                        height: 6,
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 8),

                // Address or coordinates
                Text(
                  _locationData!.address ??
                      '${_locationData!.latitude.toStringAsFixed(6)}, ${_locationData!.longitude.toStringAsFixed(6)}',
                  style: TextStyle(
                    fontSize: 14,
                    color: widget.isCurrentUser
                        ? Colors.white
                        : (isDark ? Colors.white : Colors.black87),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 4),

                // Timestamp
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Spacer(),
                    Text(
                      _formatTimestamp(widget.message.createdAt),
                      style: TextStyle(
                        fontSize: 11,
                        color: widget.isCurrentUser
                            ? Colors.white.withOpacity(0.7)
                            : (isDark ? Colors.grey[400] : Colors.grey[600]),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Open in maps button
                SizedBox(
                  width: double.infinity,
                  child: TextButton.icon(
                    onPressed: _openInMaps,
                    icon: Icon(
                      Icons.open_in_new,
                      size: 16,
                      color: widget.isCurrentUser
                          ? Colors.white
                          : theme.colorScheme.primary,
                    ),
                    label: Text(
                      'Open in Maps',
                      style: TextStyle(
                        fontSize: 12,
                        color: widget.isCurrentUser
                            ? Colors.white
                            : theme.colorScheme.primary,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      backgroundColor: widget.isCurrentUser
                          ? Colors.white.withOpacity(0.1)
                          : theme.colorScheme.primary.withOpacity(0.1),
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMapPreview() {
    final latLng = LatLng(_locationData!.latitude, _locationData!.longitude);

    return GoogleMap(
      initialCameraPosition: CameraPosition(
        target: latLng,
        zoom: 15,
      ),
      markers: {
        Marker(
          markerId: const MarkerId('location'),
          position: latLng,
          icon: BitmapDescriptor.defaultMarkerWithHue(
            _isLiveLocation
                ? BitmapDescriptor.hueGreen
                : BitmapDescriptor.hueRed,
          ),
        ),
      },
      zoomControlsEnabled: false,
      scrollGesturesEnabled: false,
      zoomGesturesEnabled: false,
      tiltGesturesEnabled: false,
      rotateGesturesEnabled: false,
      mapToolbarEnabled: false,
      myLocationButtonEnabled: false,
      compassEnabled: false,
      trafficEnabled: false,
      buildingsEnabled: false,
      indoorViewEnabled: false,
      liteModeEnabled: true, // Use lite mode for better performance
    );
  }

  Widget _buildLoadingWidget() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      constraints: const BoxConstraints(maxWidth: 280),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.isCurrentUser
            ? theme.colorScheme.primary
            : (isDark ? const Color(0xFF2A2A2A) : Colors.grey[100]),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                widget.isCurrentUser ? Colors.white : theme.colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            'Loading location...',
            style: TextStyle(
              fontSize: 14,
              color: widget.isCurrentUser
                  ? Colors.white
                  : (isDark ? Colors.white : Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${timestamp.day}/${timestamp.month}';
    }
  }
}
