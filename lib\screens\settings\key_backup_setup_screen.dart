import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/secure_key_backup_service.dart';
import '../../models/key_backup.dart';

/// Screen for setting up encryption key backup
class KeyBackupSetupScreen extends StatefulWidget {
  const KeyBackupSetupScreen({super.key});

  @override
  State<KeyBackupSetupScreen> createState() => _KeyBackupSetupScreenState();
}

class _KeyBackupSetupScreenState extends State<KeyBackupSetupScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  final _backupService = SecureKeyBackupService();
  
  late TabController _tabController;
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  RecoveryPhrase? _generatedPhrase;
  bool _phraseConfirmed = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _checkExistingBackup();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  /// Check if user already has a backup
  Future<void> _checkExistingBackup() async {
    final hasBackup = await _backupService.hasExistingBackup();
    if (hasBackup && mounted) {
      _showExistingBackupDialog();
    }
  }

  /// Show dialog for existing backup
  void _showExistingBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Backup Already Exists'),
        content: const Text(
          'You already have an encryption key backup. Creating a new backup will replace the existing one. '
          'Make sure you have access to your current backup credentials before proceeding.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  /// Generate recovery phrase
  void _generateRecoveryPhrase() {
    setState(() {
      _generatedPhrase = _backupService.generateRecoveryPhrase();
      _phraseConfirmed = false;
    });
  }

  /// Create backup with password
  Future<void> _createPasswordBackup() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final backup = await _backupService.createKeyBackup(
        masterPassword: _passwordController.text.trim(),
      );

      if (backup != null && mounted) {
        _showSuccessDialog('Password backup created successfully!');
      } else if (mounted) {
        _showErrorDialog('Failed to create backup. Please try again.');
      }

    } catch (e) {
      if (mounted) {
        _showErrorDialog('Error creating backup: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Create backup with recovery phrase
  Future<void> _createPhraseBackup() async {
    if (_generatedPhrase == null || !_phraseConfirmed) {
      _showErrorDialog('Please generate and confirm your recovery phrase first.');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final backup = await _backupService.createKeyBackup(
        masterPassword: _generatedPhrase!.toMnemonic(),
        recoveryPhrase: _generatedPhrase!.toMnemonic(),
      );

      if (backup != null && mounted) {
        _showSuccessDialog('Recovery phrase backup created successfully!');
      } else if (mounted) {
        _showErrorDialog('Failed to create backup. Please try again.');
      }

    } catch (e) {
      if (mounted) {
        _showErrorDialog('Error creating backup: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Show success dialog
  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Backup Created'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  /// Show error dialog
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Backup Failed'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Setup Key Backup'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Icon(
                Icons.backup_rounded,
                size: 64,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(height: 16),
              Text(
                'Secure Your Keys',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Create a secure backup of your encryption keys to prevent data loss',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Tab bar
              TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'Password'),
                  Tab(text: 'Recovery Phrase'),
                ],
              ),
              const SizedBox(height: 24),

              // Tab content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildPasswordTab(),
                    _buildRecoveryPhraseTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build password backup tab
  Widget _buildPasswordTab() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Create Backup Password',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          // Password field
          TextFormField(
            controller: _passwordController,
            decoration: InputDecoration(
              labelText: 'Backup Password',
              hintText: 'Enter a strong password',
              border: const OutlineInputBorder(),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscurePassword ? Icons.visibility : Icons.visibility_off,
                ),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
            ),
            obscureText: _obscurePassword,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a password';
              }
              if (value.length < 8) {
                return 'Password must be at least 8 characters';
              }
              if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)').hasMatch(value)) {
                return 'Password must contain uppercase, lowercase, and numbers';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          // Confirm password field
          TextFormField(
            controller: _confirmPasswordController,
            decoration: InputDecoration(
              labelText: 'Confirm Password',
              hintText: 'Re-enter your password',
              border: const OutlineInputBorder(),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
                ),
                onPressed: () {
                  setState(() {
                    _obscureConfirmPassword = !_obscureConfirmPassword;
                  });
                },
              ),
            ),
            obscureText: _obscureConfirmPassword,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please confirm your password';
              }
              if (value != _passwordController.text) {
                return 'Passwords do not match';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),
          
          // Create backup button
          ElevatedButton(
            onPressed: _isLoading ? null : _createPasswordBackup,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('Create Password Backup'),
          ),
          
          const SizedBox(height: 16),
          Text(
            'Remember this password! You\'ll need it to recover your keys if you reinstall the app.',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.error,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build recovery phrase backup tab
  Widget _buildRecoveryPhraseTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          'Recovery Phrase Backup',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        if (_generatedPhrase == null) ...[
          // Generate phrase button
          ElevatedButton.icon(
            onPressed: _generateRecoveryPhrase,
            icon: const Icon(Icons.generating_tokens),
            label: const Text('Generate Recovery Phrase'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'A recovery phrase is a set of 12 words that can be used to recover your encryption keys.',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ] else ...[
          // Display generated phrase
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              borderRadius: BorderRadius.circular(8),
              color: Theme.of(context).colorScheme.surface,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Your Recovery Phrase',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: _generatedPhrase!.toMnemonic()));
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Recovery phrase copied to clipboard')),
                        );
                      },
                      icon: const Icon(Icons.copy),
                      tooltip: 'Copy to clipboard',
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  _generatedPhrase!.toMnemonic(),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontFamily: 'monospace',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          
          // Confirmation checkbox
          CheckboxListTile(
            title: const Text('I have safely stored my recovery phrase'),
            subtitle: const Text('Make sure to write it down and store it securely'),
            value: _phraseConfirmed,
            onChanged: (value) {
              setState(() {
                _phraseConfirmed = value ?? false;
              });
            },
            controlAffinity: ListTileControlAffinity.leading,
          ),
          const SizedBox(height: 24),
          
          // Create backup button
          ElevatedButton(
            onPressed: (_isLoading || !_phraseConfirmed) ? null : _createPhraseBackup,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('Create Recovery Phrase Backup'),
          ),
          
          const SizedBox(height: 16),
          Text(
            'Store your recovery phrase safely! Anyone with access to it can recover your encryption keys.',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.error,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}
