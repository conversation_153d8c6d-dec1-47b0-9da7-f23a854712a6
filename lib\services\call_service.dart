import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import 'package:pulsemeet/models/call.dart';
import 'package:pulsemeet/config/supabase_config.dart';

/// Service for managing WebRTC calls and signaling
class CallService {
  static final CallService _instance = CallService._internal();
  factory CallService() => _instance;
  CallService._internal();

  final SupabaseClient _supabase = SupabaseConfig.client;
  final Uuid _uuid = const Uuid();

  // WebRTC components
  RTCPeerConnection? _peerConnection;
  MediaStream? _localStream;
  MediaStream? _remoteStream;
  RealtimeChannel? _signalingChannel;

  // Call state
  Call? _currentCall;
  final StreamController<Call?> _callStateController =
      StreamController<Call?>.broadcast();
  final StreamController<MediaStream?> _localStreamController =
      StreamController<MediaStream?>.broadcast();
  final StreamController<MediaStream?> _remoteStreamController =
      StreamController<MediaStream?>.broadcast();

  // Configuration
  final Map<String, dynamic> _iceServers = {
    'iceServers': [
      {'urls': 'stun:stun.l.google.com:19302'},
      {'urls': 'stun:stun1.l.google.com:19302'},
    ]
  };

  final Map<String, dynamic> _config = {
    'mandatory': {},
    'optional': [
      {'DtlsSrtpKeyAgreement': true},
    ]
  };

  // Getters
  Stream<Call?> get callStateStream => _callStateController.stream;
  Stream<MediaStream?> get localStreamStream => _localStreamController.stream;
  Stream<MediaStream?> get remoteStreamStream => _remoteStreamController.stream;
  Call? get currentCall => _currentCall;
  MediaStream? get localStream => _localStream;
  MediaStream? get remoteStream => _remoteStream;

  /// Initialize the call service
  Future<void> initialize() async {
    debugPrint('🔊 Initializing CallService...');

    try {
      // Initialize WebRTC
      await _initializeWebRTC();

      debugPrint('✅ CallService initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing CallService: $e');
      rethrow;
    }
  }

  /// Initialize WebRTC
  Future<void> _initializeWebRTC() async {
    // No additional initialization needed for WebRTC
    // Peer connections will be created per call
  }

  /// Start an outgoing call
  Future<void> startCall({
    required String conversationId,
    required String calleeId,
    required CallType type,
  }) async {
    debugPrint('📞 Starting ${type.name} call to $calleeId');

    try {
      final currentUserId = _supabase.auth.currentUser?.id;
      if (currentUserId == null) {
        throw Exception('User not authenticated');
      }

      // Check if there's already an active call
      if (_currentCall != null && !_currentCall!.isEnded) {
        throw Exception('Another call is already in progress');
      }

      // Create call object
      final call = Call(
        id: _uuid.v4(),
        conversationId: conversationId,
        type: type,
        state: CallState.outgoing,
        callerId: currentUserId,
        calleeId: calleeId,
        participants: [],
        createdAt: DateTime.now(),
      );

      _currentCall = call;
      _callStateController.add(call);

      // Enable wakelock during call
      await WakelockPlus.enable();

      // Setup signaling channel
      await _setupSignalingChannel(call.id);

      // Get user media
      await _getUserMedia(type);

      // Create peer connection
      await _createPeerConnection();

      // Send call invitation through signaling
      await _sendCallInvitation(call);

      debugPrint('✅ Call started successfully: ${call.id}');
    } catch (e) {
      debugPrint('❌ Error starting call: $e');
      await _endCall(CallEndReason.networkError);
      rethrow;
    }
  }

  /// Answer an incoming call
  Future<void> answerCall(String callId) async {
    debugPrint('📞 Answering call: $callId');

    try {
      if (_currentCall?.id != callId) {
        throw Exception('Call not found or already ended');
      }

      // Update call state
      _currentCall = _currentCall!.copyWith(
        state: CallState.connecting,
        startedAt: DateTime.now(),
      );
      _callStateController.add(_currentCall);

      // Enable wakelock during call
      await WakelockPlus.enable();

      // Get user media
      await _getUserMedia(_currentCall!.type);

      // Create peer connection
      await _createPeerConnection();

      // Send answer through signaling
      await _sendCallAnswer();

      debugPrint('✅ Call answered successfully');
    } catch (e) {
      debugPrint('❌ Error answering call: $e');
      await _endCall(CallEndReason.networkError);
      rethrow;
    }
  }

  /// Decline an incoming call
  Future<void> declineCall(String callId) async {
    debugPrint('📞 Declining call: $callId');

    try {
      if (_currentCall?.id != callId) {
        debugPrint('⚠️ Call not found or already ended');
        return;
      }

      // Update call state
      _currentCall = _currentCall!.copyWith(
        state: CallState.declined,
        endedAt: DateTime.now(),
        endReason: CallEndReason.declined,
      );
      _callStateController.add(_currentCall);

      // Send decline signal
      await _sendCallDecline();

      // Cleanup
      await _cleanup();

      debugPrint('✅ Call declined successfully');
    } catch (e) {
      debugPrint('❌ Error declining call: $e');
      await _cleanup();
    }
  }

  /// End the current call
  Future<void> endCall() async {
    await _endCall(CallEndReason.userEnded);
  }

  /// Internal method to end call with reason
  Future<void> _endCall(CallEndReason reason) async {
    debugPrint('📞 Ending call with reason: ${reason.name}');

    try {
      if (_currentCall != null && !_currentCall!.isEnded) {
        final endTime = DateTime.now();
        final duration = _currentCall!.startedAt != null
            ? endTime.difference(_currentCall!.startedAt!).inSeconds
            : 0;

        _currentCall = _currentCall!.copyWith(
          state: CallState.ended,
          endedAt: endTime,
          duration: duration,
          endReason: reason,
        );
        _callStateController.add(_currentCall);

        // Send end call signal
        await _sendCallEnd(reason);
      }

      // Cleanup
      await _cleanup();

      debugPrint('✅ Call ended successfully');
    } catch (e) {
      debugPrint('❌ Error ending call: $e');
      await _cleanup();
    }
  }

  /// Toggle microphone mute
  Future<void> toggleMicrophone() async {
    if (_localStream != null) {
      final audioTracks = _localStream!.getAudioTracks();
      if (audioTracks.isNotEmpty) {
        final isEnabled = audioTracks.first.enabled;
        audioTracks.first.enabled = !isEnabled;
        debugPrint('🎤 Microphone ${!isEnabled ? 'unmuted' : 'muted'}');
      }
    }
  }

  /// Toggle speaker
  Future<void> toggleSpeaker() async {
    // This will be implemented with platform-specific code
    debugPrint('🔊 Speaker toggle requested');
  }

  /// Toggle camera (for video calls)
  Future<void> toggleCamera() async {
    if (_localStream != null && _currentCall?.type == CallType.video) {
      final videoTracks = _localStream!.getVideoTracks();
      if (videoTracks.isNotEmpty) {
        final isEnabled = videoTracks.first.enabled;
        videoTracks.first.enabled = !isEnabled;
        debugPrint('📹 Camera ${!isEnabled ? 'enabled' : 'disabled'}');
      }
    }
  }

  /// Switch camera (front/back)
  Future<void> switchCamera() async {
    if (_localStream != null && _currentCall?.type == CallType.video) {
      final videoTracks = _localStream!.getVideoTracks();
      if (videoTracks.isNotEmpty) {
        await Helper.switchCamera(videoTracks.first);
        debugPrint('📹 Camera switched');
      }
    }
  }

  /// Get user media (camera/microphone)
  Future<void> _getUserMedia(CallType type) async {
    debugPrint('🎥 Getting user media for ${type.name} call');

    try {
      final constraints = {
        'audio': true,
        'video': type == CallType.video
            ? {
                'mandatory': {
                  'minWidth': '640',
                  'minHeight': '480',
                  'minFrameRate': '30',
                },
                'facingMode': 'user',
                'optional': [],
              }
            : false,
      };

      _localStream = await navigator.mediaDevices.getUserMedia(constraints);
      _localStreamController.add(_localStream);

      debugPrint('✅ User media obtained successfully');
    } catch (e) {
      debugPrint('❌ Error getting user media: $e');
      rethrow;
    }
  }

  /// Create WebRTC peer connection
  Future<void> _createPeerConnection() async {
    debugPrint('🔗 Creating peer connection');

    try {
      _peerConnection = await createPeerConnection(_iceServers, _config);

      // Add local stream
      if (_localStream != null) {
        await _peerConnection!.addStream(_localStream!);
      }

      // Handle remote stream
      _peerConnection!.onAddStream = (MediaStream stream) {
        debugPrint('📡 Remote stream added');
        _remoteStream = stream;
        _remoteStreamController.add(stream);
      };

      // Handle ICE candidates
      _peerConnection!.onIceCandidate = (RTCIceCandidate candidate) {
        debugPrint('🧊 ICE candidate generated');
        _sendIceCandidate(candidate);
      };

      // Handle connection state changes
      _peerConnection!.onConnectionState = (RTCPeerConnectionState state) {
        debugPrint('🔗 Connection state changed: $state');
        _handleConnectionStateChange(state);
      };

      debugPrint('✅ Peer connection created successfully');
    } catch (e) {
      debugPrint('❌ Error creating peer connection: $e');
      rethrow;
    }
  }

  /// Handle connection state changes
  void _handleConnectionStateChange(RTCPeerConnectionState state) {
    switch (state) {
      case RTCPeerConnectionState.RTCPeerConnectionStateConnected:
        if (_currentCall != null) {
          _currentCall = _currentCall!.copyWith(
            state: CallState.connected,
            startedAt: _currentCall!.startedAt ?? DateTime.now(),
          );
          _callStateController.add(_currentCall);
        }
        break;
      case RTCPeerConnectionState.RTCPeerConnectionStateDisconnected:
      case RTCPeerConnectionState.RTCPeerConnectionStateFailed:
        _endCall(CallEndReason.networkError);
        break;
      default:
        break;
    }
  }

  /// Setup signaling channel for call
  Future<void> _setupSignalingChannel(String callId) async {
    debugPrint('📡 Setting up signaling channel for call: $callId');

    try {
      _signalingChannel = _supabase.channel('call_signaling_$callId');

      _signalingChannel!.on(
        RealtimeListenTypes.broadcast,
        ChannelFilter(event: 'signaling'),
        (payload, [ref]) {
          _handleSignalingMessage(payload);
        },
      );

      _signalingChannel!.subscribe();
      debugPrint('✅ Signaling channel setup complete');
    } catch (e) {
      debugPrint('❌ Error setting up signaling channel: $e');
      rethrow;
    }
  }

  /// Handle incoming signaling messages
  void _handleSignalingMessage(Map<String, dynamic> payload) {
    debugPrint('📡 Received signaling message: ${payload['type']}');

    // Implementation will be added in the next phase
  }

  /// Send call invitation
  Future<void> _sendCallInvitation(Call call) async {
    // Implementation will be added in the next phase
  }

  /// Send call answer
  Future<void> _sendCallAnswer() async {
    // Implementation will be added in the next phase
  }

  /// Send call decline
  Future<void> _sendCallDecline() async {
    // Implementation will be added in the next phase
  }

  /// Send call end signal
  Future<void> _sendCallEnd(CallEndReason reason) async {
    // Implementation will be added in the next phase
  }

  /// Send ICE candidate
  Future<void> _sendIceCandidate(RTCIceCandidate candidate) async {
    // Implementation will be added in the next phase
  }

  /// Cleanup resources
  Future<void> _cleanup() async {
    debugPrint('🧹 Cleaning up call resources');

    try {
      // Close peer connection
      await _peerConnection?.close();
      _peerConnection = null;

      // Stop local stream
      await _localStream?.dispose();
      _localStream = null;
      _localStreamController.add(null);

      // Clear remote stream
      _remoteStream = null;
      _remoteStreamController.add(null);

      // Close signaling channel
      await _signalingChannel?.unsubscribe();
      _signalingChannel = null;

      // Disable wakelock
      await WakelockPlus.disable();

      debugPrint('✅ Cleanup completed');
    } catch (e) {
      debugPrint('❌ Error during cleanup: $e');
    }
  }

  /// Dispose the service
  void dispose() {
    _cleanup();
    _callStateController.close();
    _localStreamController.close();
    _remoteStreamController.close();
  }
}
