import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import 'package:pulsemeet/models/call.dart';
import 'package:pulsemeet/config/supabase_config.dart';
import 'package:pulsemeet/services/call_signaling_service.dart';

/// Service for managing WebRTC calls and signaling
class CallService {
  static final CallService _instance = CallService._internal();
  factory CallService() => _instance;
  CallService._internal();

  final SupabaseClient _supabase = SupabaseConfig.client;
  final Uuid _uuid = const Uuid();

  // Signaling service reference (will be injected)
  CallSignalingService? _signalingService;

  // WebRTC components
  RTCPeerConnection? _peerConnection;
  MediaStream? _localStream;
  MediaStream? _remoteStream;

  // Call state
  Call? _currentCall;
  final StreamController<Call?> _callStateController =
      StreamController<Call?>.broadcast();
  final StreamController<MediaStream?> _localStreamController =
      StreamController<MediaStream?>.broadcast();
  final StreamController<MediaStream?> _remoteStreamController =
      StreamController<MediaStream?>.broadcast();

  // Connection quality and monitoring
  final StreamController<String> _connectionQualityController =
      StreamController<String>.broadcast();
  Timer? _connectionMonitorTimer;
  Timer? _connectionTimeoutTimer;
  DateTime? _callStartTime;

  // Configuration
  final Map<String, dynamic> _iceServers = {
    'iceServers': [
      {'urls': 'stun:stun.l.google.com:19302'},
      {'urls': 'stun:stun1.l.google.com:19302'},
    ]
  };

  final Map<String, dynamic> _config = {
    'mandatory': {},
    'optional': [
      {'DtlsSrtpKeyAgreement': true},
    ],
    'sdpSemantics': 'unified-plan',
  };

  // Getters
  Stream<Call?> get callStateStream => _callStateController.stream;
  Stream<MediaStream?> get localStreamStream => _localStreamController.stream;
  Stream<MediaStream?> get remoteStreamStream => _remoteStreamController.stream;
  Stream<String> get connectionQualityStream =>
      _connectionQualityController.stream;
  Call? get currentCall => _currentCall;
  MediaStream? get localStream => _localStream;
  MediaStream? get remoteStream => _remoteStream;

  /// Initialize the call service
  Future<void> initialize() async {
    debugPrint('🔊 Initializing CallService...');

    try {
      // Initialize WebRTC
      await _initializeWebRTC();

      debugPrint('✅ CallService initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing CallService: $e');
      rethrow;
    }
  }

  /// Set the signaling service reference
  void setSignalingService(CallSignalingService signalingService) {
    _signalingService = signalingService;
  }

  /// Initialize WebRTC
  Future<void> _initializeWebRTC() async {
    // No additional initialization needed for WebRTC
    // Peer connections will be created per call
  }

  /// Start an outgoing call
  Future<void> startCall({
    required String conversationId,
    required String calleeId,
    required CallType type,
  }) async {
    await startCallWithId(
      callId: _uuid.v4(),
      conversationId: conversationId,
      calleeId: calleeId,
      type: type,
    );
  }

  /// Start a new call with a pre-generated call ID
  Future<Call?> startCallWithId({
    required String callId,
    required String conversationId,
    required String calleeId,
    required CallType type,
  }) async {
    debugPrint('📞 Starting ${type.name} call to $calleeId with ID: $callId');

    try {
      final currentUserId = _supabase.auth.currentUser?.id;
      if (currentUserId == null) {
        throw Exception('User not authenticated');
      }

      // Check if there's already an active call
      if (_currentCall != null && !_currentCall!.isEnded) {
        throw Exception('Another call is already in progress');
      }

      // Create call object with provided ID
      final call = Call(
        id: callId,
        conversationId: conversationId,
        type: type,
        state: CallState.outgoing,
        callerId: currentUserId,
        calleeId: calleeId,
        participants: [],
        createdAt: DateTime.now(),
      );

      _currentCall = call;
      _callStateController.add(call);

      // Enable wakelock during call
      await WakelockPlus.enable();

      // Get user media
      await _getUserMedia(type);

      // Create peer connection
      await _createPeerConnection();

      // Start connection timeout
      _startConnectionTimeout();

      // Send call invitation through signaling
      await _sendCallInvitation(call);

      debugPrint('✅ Call started successfully: ${call.id}');
      return call;
    } catch (e) {
      debugPrint('❌ Error starting call: $e');
      await _endCall(CallEndReason.networkError);
      rethrow;
    }
  }

  /// Answer an incoming call
  Future<void> answerCall(String callId) async {
    debugPrint('📞 Answering call: $callId');

    try {
      if (_currentCall?.id != callId) {
        throw Exception('Call not found or already ended');
      }

      // Update call state
      _currentCall = _currentCall!.copyWith(
        state: CallState.connecting,
        startedAt: DateTime.now(),
      );
      _callStateController.add(_currentCall);

      // Enable wakelock during call
      await WakelockPlus.enable();

      // Get user media
      await _getUserMedia(_currentCall!.type);

      // Create peer connection
      await _createPeerConnection();

      // Send answer through signaling
      await _sendCallAnswer();

      debugPrint('✅ Call answered successfully');
    } catch (e) {
      debugPrint('❌ Error answering call: $e');
      await _endCall(CallEndReason.networkError);
      rethrow;
    }
  }

  /// Decline an incoming call
  Future<void> declineCall(String callId) async {
    debugPrint('📞 Declining call: $callId');

    try {
      if (_currentCall?.id != callId) {
        debugPrint('⚠️ Call not found or already ended');
        return;
      }

      // Update call state
      _currentCall = _currentCall!.copyWith(
        state: CallState.declined,
        endedAt: DateTime.now(),
        endReason: CallEndReason.declined,
      );
      _callStateController.add(_currentCall);

      // Send decline signal
      await _sendCallDecline();

      // Cleanup
      await _cleanup();

      debugPrint('✅ Call declined successfully');
    } catch (e) {
      debugPrint('❌ Error declining call: $e');
      await _cleanup();
    }
  }

  /// End the current call
  Future<void> endCall() async {
    await _endCall(CallEndReason.userEnded);
  }

  /// Internal method to end call with reason
  Future<void> _endCall(CallEndReason reason) async {
    debugPrint('📞 Ending call with reason: ${reason.name}');

    try {
      if (_currentCall != null && !_currentCall!.isEnded) {
        final endTime = DateTime.now();
        final duration = _currentCall!.startedAt != null
            ? endTime.difference(_currentCall!.startedAt!).inSeconds
            : 0;

        _currentCall = _currentCall!.copyWith(
          state: CallState.ended,
          endedAt: endTime,
          duration: duration,
          endReason: reason,
        );
        _callStateController.add(_currentCall);

        // Send end call signal
        await _sendCallEnd(reason);
      }

      // Cleanup
      await _cleanup();

      debugPrint('✅ Call ended successfully');
    } catch (e) {
      debugPrint('❌ Error ending call: $e');
      await _cleanup();
    }
  }

  /// Toggle microphone mute
  Future<void> toggleMicrophone() async {
    if (_localStream != null) {
      final audioTracks = _localStream!.getAudioTracks();
      if (audioTracks.isNotEmpty) {
        final isEnabled = audioTracks.first.enabled;
        audioTracks.first.enabled = !isEnabled;
        debugPrint('🎤 Microphone ${!isEnabled ? 'unmuted' : 'muted'}');
      }
    }
  }

  /// Toggle speaker
  Future<void> toggleSpeaker() async {
    // This will be implemented with platform-specific code
    debugPrint('🔊 Speaker toggle requested');
  }

  /// Toggle camera (for video calls)
  Future<void> toggleCamera() async {
    if (_localStream != null && _currentCall?.type == CallType.video) {
      final videoTracks = _localStream!.getVideoTracks();
      if (videoTracks.isNotEmpty) {
        final isEnabled = videoTracks.first.enabled;
        videoTracks.first.enabled = !isEnabled;
        debugPrint('📹 Camera ${!isEnabled ? 'enabled' : 'disabled'}');
      }
    }
  }

  /// Switch camera (front/back)
  Future<void> switchCamera() async {
    if (_localStream != null && _currentCall?.type == CallType.video) {
      final videoTracks = _localStream!.getVideoTracks();
      if (videoTracks.isNotEmpty) {
        await Helper.switchCamera(videoTracks.first);
        debugPrint('📹 Camera switched');
      }
    }
  }

  /// Get user media (camera/microphone)
  Future<void> _getUserMedia(CallType type) async {
    debugPrint('🎥 Getting user media for ${type.name} call');

    try {
      final constraints = {
        'audio': true,
        'video': type == CallType.video
            ? {
                'mandatory': {
                  'minWidth': '640',
                  'minHeight': '480',
                  'minFrameRate': '30',
                },
                'facingMode': 'user',
                'optional': [],
              }
            : false,
      };

      _localStream = await navigator.mediaDevices.getUserMedia(constraints);
      _localStreamController.add(_localStream);

      debugPrint('✅ User media obtained successfully');
    } catch (e) {
      debugPrint('❌ Error getting user media: $e');
      rethrow;
    }
  }

  /// Create WebRTC peer connection
  Future<void> _createPeerConnection() async {
    debugPrint('🔗 Creating peer connection');

    try {
      _peerConnection = await createPeerConnection(_iceServers, _config);

      // Add local stream tracks (modern approach)
      if (_localStream != null) {
        for (final track in _localStream!.getTracks()) {
          await _peerConnection!.addTrack(track, _localStream!);
          debugPrint('📺 Added ${track.kind} track to peer connection');
        }
      }

      // Handle remote tracks (modern approach)
      _peerConnection!.onTrack = (RTCTrackEvent event) {
        debugPrint('📡 Remote track added: ${event.track.kind}');
        if (event.streams.isNotEmpty) {
          _remoteStream = event.streams[0];
          _remoteStreamController.add(_remoteStream);
        }
      };

      // Handle ICE candidates
      _peerConnection!.onIceCandidate = (RTCIceCandidate candidate) {
        debugPrint('🧊 ICE candidate generated');
        _sendIceCandidate(candidate);
      };

      // Handle connection state changes
      _peerConnection!.onConnectionState = (RTCPeerConnectionState state) {
        debugPrint('🔗 Connection state changed: $state');
        _handleConnectionStateChange(state);
      };

      debugPrint('✅ Peer connection created successfully');
    } catch (e) {
      debugPrint('❌ Error creating peer connection: $e');
      rethrow;
    }
  }

  /// Handle connection state changes
  void _handleConnectionStateChange(RTCPeerConnectionState state) {
    debugPrint('🔗 Connection state: $state');

    switch (state) {
      case RTCPeerConnectionState.RTCPeerConnectionStateConnected:
        if (_currentCall != null) {
          _callStartTime = DateTime.now();
          _currentCall = _currentCall!.copyWith(
            state: CallState.connected,
            startedAt: _currentCall!.startedAt ?? _callStartTime,
          );
          _callStateController.add(_currentCall);

          // Stop connection timeout and start monitoring
          _stopConnectionTimeout();
          _startConnectionMonitoring();
          _connectionQualityController.add('good');
        }
        break;
      case RTCPeerConnectionState.RTCPeerConnectionStateConnecting:
        _connectionQualityController.add('connecting');
        break;
      case RTCPeerConnectionState.RTCPeerConnectionStateDisconnected:
        _connectionQualityController.add('poor');
        debugPrint('⚠️ Connection disconnected, attempting to reconnect...');
        // Don't immediately end call, allow for reconnection
        break;
      case RTCPeerConnectionState.RTCPeerConnectionStateFailed:
        _connectionQualityController.add('failed');
        debugPrint('❌ Connection failed');
        _endCall(CallEndReason.networkError);
        break;
      case RTCPeerConnectionState.RTCPeerConnectionStateClosed:
        _connectionQualityController.add('closed');
        _stopConnectionMonitoring();
        break;
      default:
        break;
    }
  }

  /// Start connection quality monitoring
  void _startConnectionMonitoring() {
    debugPrint('📊 Starting connection quality monitoring');

    _connectionMonitorTimer?.cancel();
    _connectionMonitorTimer =
        Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkConnectionQuality();
    });
  }

  /// Stop connection quality monitoring
  void _stopConnectionMonitoring() {
    debugPrint('📊 Stopping connection quality monitoring');

    _connectionMonitorTimer?.cancel();
    _connectionMonitorTimer = null;
  }

  /// Start connection timeout timer
  void _startConnectionTimeout() {
    debugPrint('⏰ Starting connection timeout (30 seconds)');

    _connectionTimeoutTimer?.cancel();
    _connectionTimeoutTimer = Timer(const Duration(seconds: 30), () {
      debugPrint('⏰ Connection timeout reached');
      if (_currentCall != null && _currentCall!.state != CallState.connected) {
        debugPrint('❌ Call connection timed out');
        _endCall(CallEndReason.timeout);
      }
    });
  }

  /// Stop connection timeout timer
  void _stopConnectionTimeout() {
    debugPrint('⏰ Stopping connection timeout');

    _connectionTimeoutTimer?.cancel();
    _connectionTimeoutTimer = null;
  }

  /// Check connection quality
  Future<void> _checkConnectionQuality() async {
    if (_peerConnection == null) return;

    try {
      final stats = await _peerConnection!.getStats();

      // Analyze connection statistics
      String quality = 'good';

      // This is a simplified quality assessment
      // In a real implementation, you would analyze RTCStats for:
      // - Packet loss
      // - Round trip time
      // - Bandwidth
      // - Jitter

      for (final stat in stats) {
        if (stat.type == 'inbound-rtp') {
          // Check for packet loss, jitter, etc.
          final values = stat.values;
          if (values['packetsLost'] != null) {
            final packetsLost = values['packetsLost'] as int;
            final packetsReceived = values['packetsReceived'] as int? ?? 0;

            if (packetsReceived > 0) {
              final lossRate = packetsLost / (packetsLost + packetsReceived);
              if (lossRate > 0.05) {
                quality = 'poor';
              } else if (lossRate > 0.02) {
                quality = 'fair';
              }
            }
          }
        }
      }

      _connectionQualityController.add(quality);
      debugPrint('📊 Connection quality: $quality');
    } catch (e) {
      debugPrint('❌ Error checking connection quality: $e');
    }
  }

  /// Process incoming signaling data
  Future<void> processSignalingData(SignalingData data) async {
    debugPrint('📡 Processing signaling data: ${data.type}');

    if (_peerConnection == null) {
      debugPrint('⚠️ Peer connection not available, ignoring signaling data');
      return;
    }

    try {
      switch (data.type) {
        case 'offer':
          await _handleOffer(data);
          break;
        case 'answer':
          await _handleAnswer(data);
          break;
        case 'ice-candidate':
          await _handleIceCandidate(data);
          break;
        default:
          debugPrint('⚠️ Unknown signaling data type: ${data.type}');
      }
    } catch (e) {
      debugPrint('❌ Error processing signaling data: $e');
      await _endCall(CallEndReason.networkError);
    }
  }

  /// Handle incoming offer
  Future<void> _handleOffer(SignalingData data) async {
    debugPrint('📡 Handling incoming offer');

    try {
      final offer = RTCSessionDescription(data.sdp!, data.type);
      await _peerConnection!.setRemoteDescription(offer);

      // Create and send answer
      final answer = await _peerConnection!.createAnswer();
      await _peerConnection!.setLocalDescription(answer);

      // Send answer through signaling
      await _sendAnswer(answer);

      debugPrint('✅ Offer handled and answer sent');
    } catch (e) {
      debugPrint('❌ Error handling offer: $e');
      rethrow;
    }
  }

  /// Handle incoming answer
  Future<void> _handleAnswer(SignalingData data) async {
    debugPrint('📡 Handling incoming answer');

    try {
      final answer = RTCSessionDescription(data.sdp!, data.type);
      await _peerConnection!.setRemoteDescription(answer);

      debugPrint('✅ Answer handled successfully');
    } catch (e) {
      debugPrint('❌ Error handling answer: $e');
      rethrow;
    }
  }

  /// Handle incoming ICE candidate
  Future<void> _handleIceCandidate(SignalingData data) async {
    debugPrint('🧊 Handling incoming ICE candidate');

    try {
      final candidate = RTCIceCandidate(
        data.candidate!,
        data.sdpMid!,
        data.sdpMLineIndex!,
      );
      await _peerConnection!.addCandidate(candidate);

      debugPrint('✅ ICE candidate added successfully');
    } catch (e) {
      debugPrint('❌ Error handling ICE candidate: $e');
      // Don't fail the call for ICE candidate errors
    }
  }

  /// Send call invitation with offer
  Future<void> _sendCallInvitation(Call call) async {
    debugPrint('📞 Creating and sending call invitation with offer');

    try {
      // Create offer
      final offer = await _peerConnection!.createOffer();
      await _peerConnection!.setLocalDescription(offer);

      // Create signaling data for the offer
      final signalingData = SignalingData(
        type: 'offer',
        data: {
          'call_id': call.id,
          'caller_id': call.callerId,
          'callee_id': call.calleeId,
        },
        sdp: offer.sdp,
      );

      // Send through signaling service
      await _sendSignalingData(signalingData);

      debugPrint('✅ Call invitation with offer sent successfully');
    } catch (e) {
      debugPrint('❌ Error sending call invitation: $e');
      rethrow;
    }
  }

  /// Send answer
  Future<void> _sendAnswer(RTCSessionDescription answer) async {
    debugPrint('📞 Sending call answer');

    try {
      final signalingData = SignalingData(
        type: 'answer',
        data: {
          'call_id': _currentCall!.id,
          'caller_id': _currentCall!.callerId,
          'callee_id': _currentCall!.calleeId,
        },
        sdp: answer.sdp,
      );

      await _sendSignalingData(signalingData);

      debugPrint('✅ Call answer sent successfully');
    } catch (e) {
      debugPrint('❌ Error sending call answer: $e');
      rethrow;
    }
  }

  /// Send call answer (public method)
  Future<void> _sendCallAnswer() async {
    // This method is called from the public API
    // The actual answer is sent in _handleOffer after creating the answer
    debugPrint('📞 Call answer process initiated');
  }

  /// Send call decline
  Future<void> _sendCallDecline() async {
    debugPrint('📞 Sending call decline signal');
    // This is handled by the signaling service
  }

  /// Send call end signal
  Future<void> _sendCallEnd(CallEndReason reason) async {
    debugPrint('📞 Sending call end signal');
    // This is handled by the signaling service
  }

  /// Send ICE candidate
  Future<void> _sendIceCandidate(RTCIceCandidate candidate) async {
    debugPrint('🧊 Sending ICE candidate');

    try {
      final signalingData = SignalingData(
        type: 'ice-candidate',
        data: {
          'call_id': _currentCall!.id,
          'caller_id': _currentCall!.callerId,
          'callee_id': _currentCall!.calleeId,
        },
        candidate: candidate.candidate,
        sdpMid: candidate.sdpMid,
        sdpMLineIndex: candidate.sdpMLineIndex,
      );

      await _sendSignalingData(signalingData);

      debugPrint('✅ ICE candidate sent successfully');
    } catch (e) {
      debugPrint('❌ Error sending ICE candidate: $e');
      // Don't fail the call for ICE candidate errors
    }
  }

  /// Send signaling data through the signaling service
  Future<void> _sendSignalingData(SignalingData data) async {
    if (_currentCall == null) {
      debugPrint('⚠️ No current call, cannot send signaling data');
      return;
    }

    debugPrint('📡 Sending signaling data: ${data.type}');

    try {
      // Use CallSignalingService if available
      if (_signalingService != null) {
        await _signalingService!.sendSignalingData(
          callId: _currentCall!.id,
          data: data,
        );
      } else {
        debugPrint(
            '⚠️ No signaling service available, cannot send signaling data');
        throw Exception('Signaling service not available');
      }
    } catch (e) {
      debugPrint('❌ Error sending signaling data: $e');
      rethrow;
    }
  }

  /// Cleanup resources
  Future<void> _cleanup() async {
    debugPrint('🧹 Cleaning up call resources');

    try {
      // Stop connection monitoring and timeout
      _stopConnectionMonitoring();
      _stopConnectionTimeout();

      // Close peer connection
      await _peerConnection?.close();
      _peerConnection = null;

      // Stop local stream
      await _localStream?.dispose();
      _localStream = null;
      _localStreamController.add(null);

      // Clear remote stream
      _remoteStream = null;
      _remoteStreamController.add(null);

      // Reset call timing
      _callStartTime = null;

      // Disable wakelock
      await WakelockPlus.disable();

      debugPrint('✅ Cleanup completed');
    } catch (e) {
      debugPrint('❌ Error during cleanup: $e');
    }
  }

  /// Dispose the service
  void dispose() {
    _cleanup();
    _callStateController.close();
    _localStreamController.close();
    _remoteStreamController.close();
  }
}
