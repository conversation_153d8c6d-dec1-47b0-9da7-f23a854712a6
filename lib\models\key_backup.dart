import 'dart:convert';

/// Represents an encrypted backup of user's encryption keys
class EncryptedKeyBackup {
  final String backupId;
  final String userId;
  final String encryptedData; // Base64 encoded encrypted backup
  final String salt; // Base64 encoded salt for key derivation
  final String iv; // Base64 encoded initialization vector
  final String authTag; // Base64 encoded authentication tag
  final int iterations; // PBKDF2 iterations
  final String algorithm; // Encryption algorithm used
  final DateTime createdAt;
  final DateTime? expiresAt;
  final int version;
  final Map<String, dynamic> metadata;

  EncryptedKeyBackup({
    required this.backupId,
    required this.userId,
    required this.encryptedData,
    required this.salt,
    required this.iv,
    required this.authTag,
    required this.iterations,
    this.algorithm = 'aes-256-gcm',
    required this.createdAt,
    this.expiresAt,
    this.version = 1,
    Map<String, dynamic>? metadata,
  }) : metadata = metadata ?? {};

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'backup_id': backupId,
      'user_id': userId,
      'encrypted_data': encryptedData,
      'salt': salt,
      'iv': iv,
      'auth_tag': authTag,
      'iterations': iterations,
      'algorithm': algorithm,
      'created_at': createdAt.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'version': version,
      'metadata': jsonEncode(metadata),
    };
  }

  /// Create from JSON
  factory EncryptedKeyBackup.fromJson(Map<String, dynamic> json) {
    return EncryptedKeyBackup(
      backupId: json['backup_id'],
      userId: json['user_id'],
      encryptedData: json['encrypted_data'],
      salt: json['salt'],
      iv: json['iv'],
      authTag: json['auth_tag'],
      iterations: json['iterations'],
      algorithm: json['algorithm'] ?? 'aes-256-gcm',
      createdAt: DateTime.parse(json['created_at']),
      expiresAt: json['expires_at'] != null
          ? DateTime.parse(json['expires_at'])
          : null,
      version: json['version'] ?? 1,
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(jsonDecode(json['metadata']))
          : {},
    );
  }
}

/// Represents the structure of backed up keys
class KeyBackupData {
  final String userKeyPair; // JSON string of EncryptionKeyPair
  final Map<String, String> conversationKeys; // conversationId -> JSON string
  final DateTime backupTime;
  final String appVersion;
  final Map<String, dynamic> metadata;

  KeyBackupData({
    required this.userKeyPair,
    required this.conversationKeys,
    required this.backupTime,
    required this.appVersion,
    Map<String, dynamic>? metadata,
  }) : metadata = metadata ?? {};

  /// Convert to JSON for encryption
  Map<String, dynamic> toJson() {
    return {
      'user_key_pair': userKeyPair,
      'conversation_keys': conversationKeys,
      'backup_time': backupTime.toIso8601String(),
      'app_version': appVersion,
      'metadata': metadata,
    };
  }

  /// Create from JSON after decryption
  factory KeyBackupData.fromJson(Map<String, dynamic> json) {
    return KeyBackupData(
      userKeyPair: json['user_key_pair'],
      conversationKeys:
          Map<String, String>.from(json['conversation_keys'] ?? {}),
      backupTime: DateTime.parse(json['backup_time']),
      appVersion: json['app_version'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }
}

/// Recovery phrase for key backup authentication
class RecoveryPhrase {
  final List<String> words;
  final String checksum;
  final DateTime createdAt;

  RecoveryPhrase({
    required this.words,
    required this.checksum,
    required this.createdAt,
  });

  /// Generate a recovery phrase from entropy
  static RecoveryPhrase generate() {
    // Use BIP39-style word list (simplified for demo)
    const wordList = [
      'abandon',
      'ability',
      'able',
      'about',
      'above',
      'absent',
      'absorb',
      'abstract',
      'absurd',
      'abuse',
      'access',
      'accident',
      'account',
      'accuse',
      'achieve',
      'acid',
      'acoustic',
      'acquire',
      'across',
      'act',
      'action',
      'actor',
      'actress',
      'actual',
      'adapt',
      'add',
      'addict',
      'address',
      'adjust',
      'admit',
      'adult',
      'advance',
      'advice',
      'aerobic',
      'affair',
      'afford',
      'afraid',
      'again',
      'against',
      'age',
      'agent',
      'agree',
      'ahead',
      'aim',
      'air',
      'airport',
      'aisle',
      'alarm',
      'album',
      'alcohol',
      'alert',
      'alien',
      'all',
      'alley',
      'allow',
      'almost',
      'alone',
      'alpha',
      'already',
      'also',
      'alter',
      'always',
      'amateur',
      'amazing',
      'among',
      'amount',
      'amused',
      'analyst',
      'anchor',
      'ancient',
      'anger',
      'angle',
      'angry',
      'animal',
      'ankle',
      'announce',
      'annual',
      'another',
      'answer',
      'antenna',
      'antique',
      'anxiety',
      'any',
      'apart',
      'apology',
      'appear',
      'apple',
      'approve',
      'april',
      'arch',
      'arctic',
      'area',
      'arena',
      'argue',
      'arm',
      'armed',
      'armor',
      'army',
      'around',
      'arrange',
      'arrest',
      'arrive',
      'arrow',
      'art',
      'article',
      'artist',
      'artwork',
      'ask',
      'aspect',
      'assault',
      'asset',
      'assist',
      'assume',
      'asthma',
      'athlete',
      'atom',
      'attack',
      'attend',
      'attitude',
      'attract',
      'auction',
      'audit',
      'august',
      'aunt',
      'author',
      'auto',
      'autumn',
      'average',
      'avocado',
      'avoid',
      'awake',
      'aware',
      'away',
      'awesome',
      'awful',
      'awkward',
    ];

    // Generate 12 random words
    final random = DateTime.now().millisecondsSinceEpoch;
    final words = <String>[];
    var seed = random;

    for (int i = 0; i < 12; i++) {
      seed = (seed * 1103515245 + 12345) & 0x7fffffff;
      words.add(wordList[seed % wordList.length]);
    }

    // Simple checksum (in production, use proper BIP39 checksum)
    final checksum = words.join('').hashCode.toRadixString(16);

    return RecoveryPhrase(
      words: words,
      checksum: checksum,
      createdAt: DateTime.now(),
    );
  }

  /// Convert to mnemonic string
  String toMnemonic() => words.join(' ');

  /// Create from mnemonic string
  static RecoveryPhrase fromMnemonic(String mnemonic) {
    final words = mnemonic.trim().split(' ');
    if (words.length != 12) {
      throw ArgumentError('Recovery phrase must contain exactly 12 words');
    }

    final checksum = words.join('').hashCode.toRadixString(16);

    return RecoveryPhrase(
      words: words,
      checksum: checksum,
      createdAt: DateTime.now(),
    );
  }

  /// Validate recovery phrase
  bool isValid() {
    if (words.length != 12) return false;
    final expectedChecksum = words.join('').hashCode.toRadixString(16);
    return checksum == expectedChecksum;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'words': words,
      'checksum': checksum,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Create from JSON
  factory RecoveryPhrase.fromJson(Map<String, dynamic> json) {
    return RecoveryPhrase(
      words: List<String>.from(json['words']),
      checksum: json['checksum'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }
}

/// Authentication method for key recovery
enum KeyRecoveryMethod {
  password,
  pin,
  recoveryPhrase,
  biometric,
}

/// Key recovery request
class KeyRecoveryRequest {
  final String userId;
  final KeyRecoveryMethod method;
  final String credential; // password, PIN, or recovery phrase
  final String? deviceInfo;
  final DateTime requestTime;

  KeyRecoveryRequest({
    required this.userId,
    required this.method,
    required this.credential,
    this.deviceInfo,
    required this.requestTime,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'method': method.name,
      'credential': credential,
      'device_info': deviceInfo,
      'request_time': requestTime.toIso8601String(),
    };
  }

  /// Create from JSON
  factory KeyRecoveryRequest.fromJson(Map<String, dynamic> json) {
    return KeyRecoveryRequest(
      userId: json['user_id'],
      method: KeyRecoveryMethod.values.firstWhere(
        (e) => e.name == json['method'],
      ),
      credential: json['credential'],
      deviceInfo: json['device_info'],
      requestTime: DateTime.parse(json['request_time']),
    );
  }
}
