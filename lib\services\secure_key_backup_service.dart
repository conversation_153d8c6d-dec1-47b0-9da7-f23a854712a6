import 'dart:convert';
import 'dart:typed_data';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:pointycastle/export.dart';
import '../models/key_backup.dart';
import 'encryption_service.dart';
import 'key_management_service.dart';

/// Service for secure backup and recovery of encryption keys
/// Implements end-to-end encryption for key backups using user-derived master keys
class SecureKeyBackupService {
  static final SecureKeyBackupService _instance =
      SecureKeyBackupService._internal();
  factory SecureKeyBackupService() => _instance;
  SecureKeyBackupService._internal();

  final _supabase = Supabase.instance.client;
  final _encryptionService = EncryptionService();
  final _keyManagementService = KeyManagementService();

  // Secure storage for recovery credentials
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Key derivation parameters
  static const int _defaultIterations = 100000; // PBKDF2 iterations
  static const int _saltLength = 32; // 256-bit salt
  static const int _keyLength = 32; // 256-bit key
  static const String _backupVersion = '1.0';

  bool _isInitialized = false;

  /// Initialize the backup service
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _encryptionService.initialize();
    await _ensureBackupTables();

    _isInitialized = true;
    debugPrint('🔐 SecureKeyBackupService initialized');
  }

  /// Create encrypted backup of all user keys
  Future<EncryptedKeyBackup?> createKeyBackup({
    required String masterPassword,
    String? recoveryPhrase,
  }) async {
    try {
      await initialize();

      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        debugPrint('❌ Cannot create backup: user not authenticated');
        return null;
      }

      debugPrint('🔐 Creating encrypted key backup for user: $userId');

      // 1. Collect all keys to backup
      final backupData = await _collectKeysForBackup();
      if (backupData == null) {
        debugPrint('❌ No keys found to backup');
        return null;
      }

      // 2. Generate salt and derive master key
      final salt = _generateRandomBytes(_saltLength);
      final masterKey = await _deriveMasterKey(masterPassword, salt);

      // 3. Encrypt the backup data
      final encryptedBackup = await _encryptBackupData(backupData, masterKey);

      // 4. Create backup record
      final backup = EncryptedKeyBackup(
        backupId: _generateBackupId(),
        userId: userId,
        encryptedData: base64Encode(encryptedBackup.ciphertext),
        salt: base64Encode(salt),
        iv: base64Encode(encryptedBackup.iv),
        authTag: base64Encode(encryptedBackup.authTag),
        iterations: _defaultIterations,
        createdAt: DateTime.now(),
        expiresAt:
            DateTime.now().add(const Duration(days: 365)), // 1 year expiry
        metadata: {
          'app_version': _backupVersion,
          'key_count': backupData.conversationKeys.length + 1,
          'backup_method': recoveryPhrase != null ? 'phrase' : 'password',
        },
      );

      // 5. Store backup on server
      await _storeBackupOnServer(backup);

      // 6. Store recovery phrase locally if provided
      if (recoveryPhrase != null) {
        await _storeRecoveryPhrase(userId, recoveryPhrase);
      }

      debugPrint('✅ Key backup created successfully: ${backup.backupId}');
      return backup;
    } catch (e) {
      debugPrint('❌ Error creating key backup: $e');
      return null;
    }
  }

  /// Restore keys from encrypted backup
  Future<bool> restoreKeysFromBackup({
    required String masterPassword,
    String? recoveryPhrase,
    String? backupId,
  }) async {
    try {
      await initialize();

      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        debugPrint('❌ Cannot restore backup: user not authenticated');
        return false;
      }

      debugPrint('🔄 Restoring keys from backup for user: $userId');

      // 1. Find the backup to restore
      final backup = await _findBackupForUser(userId, backupId);
      if (backup == null) {
        debugPrint('❌ No backup found for user');
        return false;
      }

      // 2. Derive master key using stored salt
      final salt = base64Decode(backup.salt);
      final masterKey = await _deriveMasterKey(masterPassword, salt);

      // 3. Decrypt the backup data
      final backupData = await _decryptBackupData(backup, masterKey);
      if (backupData == null) {
        debugPrint('❌ Failed to decrypt backup - invalid password?');
        return false;
      }

      // 4. Restore the keys
      await _restoreKeysFromData(backupData);

      debugPrint(
          '✅ Keys restored successfully from backup: ${backup.backupId}');
      return true;
    } catch (e) {
      debugPrint('❌ Error restoring keys from backup: $e');
      return false;
    }
  }

  /// Check if user has existing backups
  Future<bool> hasExistingBackup() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) return false;

      final response = await _supabase
          .from('encrypted_key_backups')
          .select('backup_id')
          .eq('user_id', userId)
          .limit(1);

      return response.isNotEmpty;
    } catch (e) {
      debugPrint('❌ Error checking for existing backup: $e');
      return false;
    }
  }

  /// Generate recovery phrase for backup authentication
  RecoveryPhrase generateRecoveryPhrase() {
    return RecoveryPhrase.generate();
  }

  /// Validate recovery phrase
  bool validateRecoveryPhrase(String phrase) {
    try {
      final recoveryPhrase = RecoveryPhrase.fromMnemonic(phrase);
      return recoveryPhrase.isValid();
    } catch (e) {
      return false;
    }
  }

  /// Auto-backup keys after key generation/update
  Future<void> autoBackupKeys() async {
    try {
      // Check if auto-backup is enabled and user has set up backup
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) return;

      final hasBackup = await hasExistingBackup();
      if (!hasBackup) {
        debugPrint('🔐 No backup configured, skipping auto-backup');
        return;
      }

      // Get stored master password/recovery phrase
      final storedCredential = await _getStoredBackupCredential(userId);
      if (storedCredential == null) {
        debugPrint('🔐 No backup credential stored, skipping auto-backup');
        return;
      }

      // Create updated backup
      await createKeyBackup(masterPassword: storedCredential);
      debugPrint('✅ Auto-backup completed');
    } catch (e) {
      debugPrint('❌ Error during auto-backup: $e');
    }
  }

  /// Derive master key from password using PBKDF2
  Future<Uint8List> _deriveMasterKey(String password, Uint8List salt) async {
    final passwordBytes = utf8.encode(password);

    final pbkdf2 = PBKDF2KeyDerivator(HMac(SHA256Digest(), 64));
    pbkdf2.init(Pbkdf2Parameters(salt, _defaultIterations, _keyLength));

    return pbkdf2.process(Uint8List.fromList(passwordBytes));
  }

  /// Collect all keys that need to be backed up
  Future<KeyBackupData?> _collectKeysForBackup() async {
    try {
      // Get user key pair
      final userKeyPair = _keyManagementService.currentUserKeyPair;
      if (userKeyPair == null) {
        debugPrint('❌ No user key pair found');
        return null;
      }

      // Get all conversation keys from secure storage
      final conversationKeys = <String, String>{};

      // Read all stored conversation keys
      final allKeys = await _secureStorage.readAll();
      for (final entry in allKeys.entries) {
        if (entry.key.startsWith('conversation_key_')) {
          final conversationId =
              entry.key.substring('conversation_key_'.length);
          conversationKeys[conversationId] = entry.value;
        }
      }

      return KeyBackupData(
        userKeyPair: jsonEncode(userKeyPair.toJson()),
        conversationKeys: conversationKeys,
        backupTime: DateTime.now(),
        appVersion: _backupVersion,
        metadata: {
          'device_info': 'Flutter App',
          'backup_type': 'full',
        },
      );
    } catch (e) {
      debugPrint('❌ Error collecting keys for backup: $e');
      return null;
    }
  }

  /// Encrypt backup data using AES-256-GCM
  Future<_EncryptedData> _encryptBackupData(
    KeyBackupData data,
    Uint8List masterKey,
  ) async {
    final plaintext = utf8.encode(jsonEncode(data.toJson()));
    final iv = _generateRandomBytes(12); // 96-bit IV for GCM

    final cipher = GCMBlockCipher(AESEngine());
    final params = AEADParameters(
      KeyParameter(masterKey),
      128, // 128-bit auth tag
      iv,
      Uint8List(0), // No additional authenticated data
    );

    cipher.init(true, params);

    final ciphertext = cipher.process(Uint8List.fromList(plaintext));
    final authTag =
        Uint8List.fromList(ciphertext.sublist(ciphertext.length - 16));
    final encryptedData =
        Uint8List.fromList(ciphertext.sublist(0, ciphertext.length - 16));

    return _EncryptedData(
      ciphertext: encryptedData,
      iv: iv,
      authTag: authTag,
    );
  }

  /// Generate random bytes
  Uint8List _generateRandomBytes(int length) {
    final random = Random.secure();
    return Uint8List.fromList(
      List.generate(length, (_) => random.nextInt(256)),
    );
  }

  /// Generate unique backup ID
  String _generateBackupId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(999999);
    return 'backup_${timestamp}_$random';
  }

  /// Decrypt backup data using AES-256-GCM
  Future<KeyBackupData?> _decryptBackupData(
    EncryptedKeyBackup backup,
    Uint8List masterKey,
  ) async {
    try {
      final ciphertext = base64Decode(backup.encryptedData);
      final iv = base64Decode(backup.iv);
      final authTag = base64Decode(backup.authTag);

      // Combine ciphertext and auth tag for GCM
      final encryptedWithTag = Uint8List.fromList([...ciphertext, ...authTag]);

      final cipher = GCMBlockCipher(AESEngine());
      final params = AEADParameters(
        KeyParameter(masterKey),
        128, // 128-bit auth tag
        iv,
        Uint8List(0), // No additional authenticated data
      );

      cipher.init(false, params);

      final decrypted = cipher.process(encryptedWithTag);
      final jsonString = utf8.decode(decrypted);
      final jsonData = jsonDecode(jsonString);

      return KeyBackupData.fromJson(jsonData);
    } catch (e) {
      debugPrint('❌ Error decrypting backup data: $e');
      return null;
    }
  }

  /// Store backup on server
  Future<void> _storeBackupOnServer(EncryptedKeyBackup backup) async {
    await _supabase.from('encrypted_key_backups').upsert(backup.toJson());
  }

  /// Find backup for user
  Future<EncryptedKeyBackup?> _findBackupForUser(
      String userId, String? backupId) async {
    try {
      late final List<Map<String, dynamic>> response;

      if (backupId != null) {
        response = await _supabase
            .from('encrypted_key_backups')
            .select()
            .eq('user_id', userId)
            .eq('backup_id', backupId);
      } else {
        // Get the most recent backup
        response = await _supabase
            .from('encrypted_key_backups')
            .select()
            .eq('user_id', userId)
            .order('created_at', ascending: false)
            .limit(1);
      }

      if (response.isEmpty) return null;

      return EncryptedKeyBackup.fromJson(response.first);
    } catch (e) {
      debugPrint('❌ Error finding backup: $e');
      return null;
    }
  }

  /// Restore keys from backup data
  Future<void> _restoreKeysFromData(KeyBackupData backupData) async {
    try {
      // Restore user key pair
      final userId = _supabase.auth.currentUser?.id;
      if (userId != null) {
        await _secureStorage.write(
          key: 'user_keypair_$userId',
          value: backupData.userKeyPair,
        );
        debugPrint('✅ Restored user key pair');
      }

      // Restore conversation keys
      for (final entry in backupData.conversationKeys.entries) {
        await _secureStorage.write(
          key: 'conversation_key_${entry.key}',
          value: entry.value,
        );
      }

      debugPrint(
          '✅ Restored ${backupData.conversationKeys.length} conversation keys');

      // Reinitialize key management service with restored keys
      await _keyManagementService.initialize();
    } catch (e) {
      debugPrint('❌ Error restoring keys: $e');
      rethrow;
    }
  }

  /// Store recovery phrase locally
  Future<void> _storeRecoveryPhrase(String userId, String phrase) async {
    await _secureStorage.write(
      key: 'recovery_phrase_$userId',
      value: phrase,
    );
  }

  /// Get stored backup credential
  Future<String?> _getStoredBackupCredential(String userId) async {
    // Try recovery phrase first
    final phrase = await _secureStorage.read(key: 'recovery_phrase_$userId');
    if (phrase != null) return phrase;

    // Try stored password (if user opted to store it)
    return await _secureStorage.read(key: 'backup_password_$userId');
  }

  /// Ensure backup tables exist
  Future<void> _ensureBackupTables() async {
    try {
      await _supabase.rpc('execute_sql', params: {
        'sql': '''
          CREATE TABLE IF NOT EXISTS encrypted_key_backups (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            backup_id VARCHAR(255) UNIQUE NOT NULL,
            user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            encrypted_data TEXT NOT NULL,
            salt VARCHAR(255) NOT NULL,
            iv VARCHAR(255) NOT NULL,
            auth_tag VARCHAR(255) NOT NULL,
            iterations INTEGER NOT NULL DEFAULT 100000,
            algorithm VARCHAR(50) NOT NULL DEFAULT 'aes-256-gcm',
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            expires_at TIMESTAMP WITH TIME ZONE,
            version INTEGER NOT NULL DEFAULT 1,
            metadata JSONB DEFAULT '{}'::jsonb,

            -- Indexes for efficient lookups
            INDEX idx_key_backups_user_id ON encrypted_key_backups(user_id),
            INDEX idx_key_backups_created ON encrypted_key_backups(user_id, created_at DESC)
          );

          -- Enable RLS
          ALTER TABLE encrypted_key_backups ENABLE ROW LEVEL SECURITY;

          -- Users can only access their own backups
          CREATE POLICY "Users can manage their own key backups" ON encrypted_key_backups
            FOR ALL TO authenticated
            USING (user_id = auth.uid())
            WITH CHECK (user_id = auth.uid());
        '''
      });

      debugPrint('✅ Key backup tables ensured');
    } catch (e) {
      debugPrint('❌ Error ensuring backup tables: $e');
    }
  }
}

/// Internal class for encrypted data
class _EncryptedData {
  final Uint8List ciphertext;
  final Uint8List iv;
  final Uint8List authTag;

  _EncryptedData({
    required this.ciphertext,
    required this.iv,
    required this.authTag,
  });
}
