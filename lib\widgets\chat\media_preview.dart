import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:pulsemeet/models/message.dart';
import 'package:pulsemeet/models/encryption_key.dart';
import 'package:pulsemeet/screens/media/media_viewer_screen.dart';
import 'package:pulsemeet/services/media_service.dart';

/// A widget that displays a preview of media content (image or video)
class MediaPreview extends StatefulWidget {
  final MediaData mediaData;
  final bool isFromCurrentUser;
  final double maxWidth;
  final double maxHeight;
  final String? conversationId;
  final ConversationType? conversationType;

  const MediaPreview({
    super.key,
    required this.mediaData,
    required this.isFromCurrentUser,
    this.maxWidth = 240.0,
    this.maxHeight = 320.0,
    this.conversationId,
    this.conversationType,
  });

  @override
  State<MediaPreview> createState() => _MediaPreviewState();
}

class _MediaPreviewState extends State<MediaPreview>
    with AutomaticKeepAliveClientMixin {
  // Cache the decrypted URL to prevent re-decryption on rebuilds
  String? _cachedDecryptedUrl;
  bool _isDecrypting = false;

  @override
  bool get wantKeepAlive => true; // Keep widget alive to prevent rebuilds

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    // Determine if this is an image or video
    final bool isImage = widget.mediaData.isImage;
    final bool isVideo = widget.mediaData.isVideo;

    // Calculate aspect ratio if dimensions are available
    double aspectRatio = 1.0;
    if (widget.mediaData.width != null &&
        widget.mediaData.height != null &&
        widget.mediaData.height! > 0) {
      aspectRatio = widget.mediaData.width! / widget.mediaData.height!;
    }

    // Calculate dimensions
    double width = widget.maxWidth;
    double height = width / aspectRatio;

    if (height > widget.maxHeight) {
      height = widget.maxHeight;
      width = height * aspectRatio;
    }

    return GestureDetector(
      onTap: () => _openMediaViewer(context),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.black12,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16.0),
            topRight: Radius.circular(16.0),
          ),
        ),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Media preview
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16.0),
                topRight: Radius.circular(16.0),
              ),
              child: _buildMediaPreview(isImage, isVideo),
            ),

            // Video indicator
            if (isVideo)
              Center(
                child: Container(
                  padding: const EdgeInsets.all(8.0),
                  decoration: BoxDecoration(
                    color: Colors.black45,
                    borderRadius: BorderRadius.circular(24.0),
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    color: Colors.white,
                    size: 32.0,
                  ),
                ),
              ),

            // Duration indicator for videos
            if (isVideo && widget.mediaData.duration != null)
              Positioned(
                bottom: 8.0,
                right: 8.0,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6.0,
                    vertical: 2.0,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black45,
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: Text(
                    _formatDuration(widget.mediaData.duration!),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12.0,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Build the media preview based on type
  Widget _buildMediaPreview(bool isImage, bool isVideo) {
    // Check if this is a local file (during sending phase)
    final bool isLocalFile = widget.mediaData.url.startsWith('file://');

    if (isImage) {
      if (isLocalFile) {
        // Display local image file
        final String localPath =
            widget.mediaData.url.replaceFirst('file://', '');
        return Image.file(
          File(localPath),
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => const Center(
            child: Icon(Icons.broken_image, color: Colors.white70, size: 48.0),
          ),
        );
      } else {
        // Display remote image with potential decryption
        return _buildEncryptedImageWidget();
      }
    } else if (isVideo) {
      if (isLocalFile) {
        // For local videos, we don't generate thumbnails yet, so show a placeholder
        // We'll use the file path later when we implement local video thumbnails
        return Stack(
          fit: StackFit.expand,
          children: [
            Container(
              color: Colors.black26,
              child: const Center(
                child: Icon(
                  Icons.video_file,
                  size: 48.0,
                  color: Colors.white70,
                ),
              ),
            ),
            // Add an uploading indicator
            const Center(
              child: Padding(
                padding: EdgeInsets.only(top: 60.0),
                child: Text(
                  "Uploading...",
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
              ),
            ),
          ],
        );
      } else {
        // For remote videos, show the thumbnail if available
        if (widget.mediaData.thumbnailUrl != null) {
          return CachedNetworkImage(
            imageUrl: widget.mediaData.thumbnailUrl!,
            fit: BoxFit.cover,
            placeholder: (context, url) => const Center(
              child: CircularProgressIndicator(),
            ),
            errorWidget: (context, url, error) => const Center(
              child: Icon(Icons.video_file),
            ),
          );
        } else {
          // Fallback to a video icon
          return Container(
            color: Colors.black12,
            child: const Center(
              child: Icon(
                Icons.video_file,
                size: 48.0,
                color: Colors.white70,
              ),
            ),
          );
        }
      }
    } else if (widget.mediaData.isAudio) {
      // For audio files, show an audio icon
      return Container(
        color: Colors.black12,
        child: const Center(
          child: Icon(
            Icons.audiotrack,
            size: 48.0,
            color: Colors.white70,
          ),
        ),
      );
    } else {
      // Unsupported media type
      return Container(
        color: Colors.black12,
        child: const Center(
          child: Icon(
            Icons.help,
            size: 48.0,
            color: Colors.white70,
          ),
        ),
      );
    }
  }

  /// Open the media viewer screen
  void _openMediaViewer(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MediaViewerScreen(
          mediaData: widget.mediaData,
          conversationId: widget.conversationId,
          conversationType: widget.conversationType,
        ),
      ),
    );
  }

  /// Build encrypted image widget with decryption and retry mechanism
  Widget _buildEncryptedImageWidget() {
    debugPrint(
        '🖼️ Building encrypted image widget for ${widget.mediaData.url}');

    // If we don't have conversation context, fall back to regular display
    if (widget.conversationId == null || widget.conversationType == null) {
      debugPrint(
          '⚠️ No conversation context, using regular CachedNetworkImage');
      return CachedNetworkImage(
        imageUrl: widget.mediaData.url,
        fit: BoxFit.cover,
        placeholder: (context, url) => const Center(
          child: CircularProgressIndicator(),
        ),
        errorWidget: (context, url, error) => const Center(
          child: Icon(Icons.error),
        ),
      );
    }

    // Use FutureBuilder to handle decryption with retry
    return FutureBuilder<String?>(
      key: ValueKey(
          'decrypt_${widget.mediaData.url}'), // Stable key for FutureBuilder
      future: _getDecryptedUrlWithRetry(),
      builder: (context, snapshot) {
        debugPrint(
            '🔄 FutureBuilder state: ${snapshot.connectionState}, hasData: ${snapshot.hasData}, hasError: ${snapshot.hasError}');

        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          debugPrint('❌ FutureBuilder error or no data: ${snapshot.error}');
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, color: Colors.white70, size: 48.0),
                SizedBox(height: 8),
                Text(
                  'Failed to decrypt image',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          );
        }

        final decryptedUrl = snapshot.data!;
        debugPrint('✅ Using decrypted URL: $decryptedUrl');

        // If it's a local file after decryption, use Image.file
        if (decryptedUrl.startsWith('file://')) {
          final localPath = decryptedUrl.replaceFirst('file://', '');
          final imageFile = File(localPath);

          // Validate the decrypted image file before displaying
          return FutureBuilder<bool>(
            future: _validateDecryptedImageFile(imageFile),
            builder: (context, validationSnapshot) {
              if (validationSnapshot.connectionState ==
                  ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (validationSnapshot.data != true) {
                debugPrint(
                    '❌ Decrypted image file validation failed: $localPath');
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.lock_outline,
                          color: Colors.white70, size: 48.0),
                      SizedBox(height: 8),
                      Text(
                        'Media unavailable',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Decryption failed',
                        style: TextStyle(
                          color: Colors.white54,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return Image.file(
                imageFile,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  debugPrint('❌ Error displaying decrypted image: $error');
                  debugPrint('❌ Stack trace: $stackTrace');
                  // Check if this is a decryption-related error
                  final isDecryptionError =
                      error.toString().contains('Invalid image data') ||
                          error.toString().contains('MAC') ||
                          error.toString().contains('Key ID mismatch') ||
                          error.toString().contains('Decryption failed');

                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          isDecryptionError
                              ? Icons.lock_outline
                              : Icons.broken_image,
                          color: Colors.white70,
                          size: 48.0,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          isDecryptionError
                              ? 'Media unavailable'
                              : 'Image display error',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (isDecryptionError) ...[
                          const SizedBox(height: 4),
                          const Text(
                            'Encryption keys changed',
                            style: TextStyle(
                              color: Colors.white54,
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ],
                    ),
                  );
                },
              );
            },
          );
        } else {
          // Use CachedNetworkImage for remote URLs
          return CachedNetworkImage(
            imageUrl: decryptedUrl,
            fit: BoxFit.cover,
            placeholder: (context, url) => const Center(
              child: CircularProgressIndicator(),
            ),
            errorWidget: (context, url, error) {
              debugPrint('❌ CachedNetworkImage error for URL: $url');
              debugPrint('❌ Error details: $error');
              // Check if this is a decryption-related error
              final isDecryptionError =
                  error.toString().contains('Invalid image data') ||
                      error.toString().contains('MAC') ||
                      error.toString().contains('Key ID mismatch') ||
                      error.toString().contains('Decryption failed');

              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      isDecryptionError
                          ? Icons.lock_outline
                          : Icons.broken_image,
                      color: Colors.white70,
                      size: 48.0,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      isDecryptionError
                          ? 'Media unavailable'
                          : 'Failed to load image',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (isDecryptionError) ...[
                      const SizedBox(height: 4),
                      const Text(
                        'Encryption keys changed',
                        style: TextStyle(
                          color: Colors.white54,
                          fontSize: 10,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              );
            },
          );
        }
      },
    );
  }

  /// Get decrypted URL with retry mechanism
  Future<String?> _getDecryptedUrlWithRetry({int maxRetries = 3}) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint(
            '🔄 Decryption attempt $attempt/$maxRetries for ${widget.mediaData.url}');

        final decryptedUrl = await _getDecryptedUrl();
        if (decryptedUrl != null) {
          debugPrint('✅ Decryption successful on attempt $attempt');
          return decryptedUrl;
        }

        debugPrint('❌ Decryption failed on attempt $attempt');

        // Wait before retrying (exponential backoff)
        if (attempt < maxRetries) {
          await Future.delayed(Duration(milliseconds: 500 * attempt));
        }
      } catch (e) {
        debugPrint('❌ Decryption error on attempt $attempt: $e');

        if (attempt < maxRetries) {
          await Future.delayed(Duration(milliseconds: 500 * attempt));
        } else {
          rethrow;
        }
      }
    }

    return null;
  }

  /// Get decrypted URL for media with caching
  Future<String?> _getDecryptedUrl() async {
    // Return cached URL if available and it's a local file (properly decrypted)
    if (_cachedDecryptedUrl != null &&
        _cachedDecryptedUrl!.startsWith('file://')) {
      debugPrint('📁 Using cached decrypted URL for ${widget.mediaData.url}');
      return _cachedDecryptedUrl;
    }

    // Clear invalid cache (non-local URLs are not properly decrypted)
    if (_cachedDecryptedUrl != null &&
        !_cachedDecryptedUrl!.startsWith('file://')) {
      debugPrint(
          '🗑️ Clearing invalid cached URL (not decrypted): $_cachedDecryptedUrl');
      _cachedDecryptedUrl = null;
    }

    if (widget.conversationId == null || widget.conversationType == null) {
      return widget.mediaData.url;
    }

    if (_isDecrypting) {
      debugPrint(
          '⏳ Decryption already in progress for ${widget.mediaData.url}');
      return null;
    }

    try {
      _isDecrypting = true;
      debugPrint(
          '🔑 MediaPreview using conversation ID: ${widget.conversationId}');
      debugPrint(
          '🔑 MediaPreview using conversation type: ${widget.conversationType}');
      debugPrint('🔑 Media URL: ${widget.mediaData.url}');

      // Extract conversation ID from media URL path for reference
      String? urlConversationId =
          _extractConversationIdFromUrl(widget.mediaData.url);

      // CRITICAL FIX: Use the URL conversation ID (where the media was encrypted) for decryption
      // instead of the message conversation ID (where the message is displayed)
      // This fixes the conversation key mismatch issue
      String conversationIdForDecryption =
          urlConversationId ?? widget.conversationId!;

      debugPrint('🔑 URL conversation ID: $urlConversationId');
      debugPrint('🔑 Message conversation ID: ${widget.conversationId}');
      debugPrint(
          '🔑 Using conversation ID for decryption: $conversationIdForDecryption');

      if (urlConversationId != null &&
          urlConversationId != widget.conversationId) {
        debugPrint('⚠️ CONVERSATION ID MISMATCH DETECTED!');
        debugPrint('⚠️ Media was encrypted with: $urlConversationId');
        debugPrint('⚠️ Chat context expects: ${widget.conversationId}');
        debugPrint('⚠️ Using URL conversation ID for correct decryption');
      }

      final mediaService = MediaService();
      final decryptedUrl = await mediaService.getDecryptedMediaUrl(
        widget.mediaData,
        conversationIdForDecryption,
        widget.conversationType!,
      );

      // Cache the result
      if (decryptedUrl != null) {
        _cachedDecryptedUrl = decryptedUrl;
        debugPrint('💾 Cached decrypted URL for ${widget.mediaData.url}');
      }

      return decryptedUrl ?? widget.mediaData.url;
    } catch (e) {
      debugPrint('Error getting decrypted media URL: $e');
      return widget.mediaData.url; // Fallback to original URL
    } finally {
      _isDecrypting = false;
    }
  }

  /// Validate a decrypted image file to ensure it's not corrupted
  Future<bool> _validateDecryptedImageFile(File imageFile) async {
    try {
      // Check if file exists and has content
      if (!await imageFile.exists()) {
        debugPrint('❌ Decrypted image file does not exist: ${imageFile.path}');
        return false;
      }

      final fileSize = await imageFile.length();
      if (fileSize == 0) {
        debugPrint('❌ Decrypted image file is empty: ${imageFile.path}');
        return false;
      }

      // Check minimum file size (1KB)
      if (fileSize < 1024) {
        debugPrint('❌ Decrypted image file too small: $fileSize bytes');
        return false;
      }

      // Try to read the first few bytes to check for valid image headers
      final bytes = await imageFile.readAsBytes();
      if (bytes.isEmpty) {
        debugPrint('❌ Could not read decrypted image file bytes');
        return false;
      }

      // Check for valid image file headers
      if (bytes.length >= 2) {
        // JPEG: FF D8
        if (bytes[0] == 0xFF && bytes[1] == 0xD8) {
          debugPrint('✅ Valid JPEG image detected');
          return true;
        }

        // PNG: 89 50 4E 47
        if (bytes.length >= 4 &&
            bytes[0] == 0x89 &&
            bytes[1] == 0x50 &&
            bytes[2] == 0x4E &&
            bytes[3] == 0x47) {
          debugPrint('✅ Valid PNG image detected');
          return true;
        }

        // GIF: 47 49 46 38
        if (bytes.length >= 4 &&
            bytes[0] == 0x47 &&
            bytes[1] == 0x49 &&
            bytes[2] == 0x46 &&
            bytes[3] == 0x38) {
          debugPrint('✅ Valid GIF image detected');
          return true;
        }

        // WebP: 52 49 46 46 (RIFF)
        if (bytes.length >= 12 &&
            bytes[0] == 0x52 &&
            bytes[1] == 0x49 &&
            bytes[2] == 0x46 &&
            bytes[3] == 0x46 &&
            bytes[8] == 0x57 &&
            bytes[9] == 0x45 &&
            bytes[10] == 0x42 &&
            bytes[11] == 0x50) {
          debugPrint('✅ Valid WebP image detected');
          return true;
        }
      }

      debugPrint(
          '⚠️ Decrypted image file format not recognized, but allowing anyway');
      return true; // Allow unknown formats to pass through
    } catch (e) {
      debugPrint('❌ Error validating decrypted image file: $e');
      return false;
    }
  }

  /// Extract conversation ID from media URL path
  String? _extractConversationIdFromUrl(String url) {
    try {
      // URL format: https://domain/storage/v1/object/public/pulse_media/direct_CONVERSATION_ID/filename
      // or: https://domain/storage/v1/object/public/pulse_media/conversations/CONVERSATION_ID/filename
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;

      // Find the pulse_media segment
      final pulseMediaIndex = pathSegments.indexOf('pulse_media');
      if (pulseMediaIndex == -1 || pulseMediaIndex + 1 >= pathSegments.length) {
        return null;
      }

      // Get the next segment after pulse_media
      final nextSegment = pathSegments[pulseMediaIndex + 1];

      // Extract conversation ID from direct_CONVERSATION_ID or conversations/CONVERSATION_ID
      if (nextSegment.startsWith('direct_')) {
        return nextSegment.substring('direct_'.length);
      } else if (nextSegment == 'conversations' &&
          pulseMediaIndex + 2 < pathSegments.length) {
        return pathSegments[pulseMediaIndex + 2];
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error extracting conversation ID from URL: $e');
      return null;
    }
  }

  /// Format duration in seconds to MM:SS
  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}
