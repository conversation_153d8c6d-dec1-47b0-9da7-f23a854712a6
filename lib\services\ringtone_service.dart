import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:audio_session/audio_session.dart';
import 'package:vibration/vibration.dart';

/// Service for managing call ringtones and audio feedback
class RingtoneService {
  static final RingtoneService _instance = RingtoneService._internal();
  factory RingtoneService() => _instance;
  RingtoneService._internal();

  FlutterSoundPlayer? _player;
  bool _isInitialized = false;
  bool _isRinging = false;
  Timer? _vibrationTimer;
  StreamSubscription? _playerSubscription;

  /// Initialize the ringtone service
  Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('🔔 Initializing RingtoneService...');

    try {
      _player = FlutterSoundPlayer();
      await _player!.openPlayer();

      // Configure audio session for ringtone playback
      await _configureAudioSession();

      _isInitialized = true;
      debugPrint('✅ RingtoneService initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing RingtoneService: $e');
      rethrow;
    }
  }

  /// Configure audio session for ringtone playback
  Future<void> _configureAudioSession() async {
    try {
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration(
        avAudioSessionCategory: AVAudioSessionCategory.playback,
        avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.mixWithOthers,
        avAudioSessionMode: AVAudioSessionMode.defaultMode,
        avAudioSessionRouteSharingPolicy: AVAudioSessionRouteSharingPolicy.defaultPolicy,
        avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
        androidAudioAttributes: AndroidAudioAttributes(
          contentType: AndroidAudioContentType.sonification,
          flags: AndroidAudioFlags.none,
          usage: AndroidAudioUsage.notification,
        ),
        androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
        androidWillPauseWhenDucked: false,
      ));

      debugPrint('🔔 Audio session configured for ringtone playback');
    } catch (e) {
      debugPrint('❌ Error configuring audio session: $e');
    }
  }

  /// Start playing incoming call ringtone
  Future<void> startIncomingCallRingtone() async {
    if (!_isInitialized || _isRinging) return;

    debugPrint('🔔 Starting incoming call ringtone...');

    try {
      _isRinging = true;

      // Start vibration pattern
      _startVibrationPattern();

      // Play system ringtone sound
      await _playSystemRingtone();

      debugPrint('✅ Incoming call ringtone started');
    } catch (e) {
      debugPrint('❌ Error starting ringtone: $e');
      _isRinging = false;
    }
  }

  /// Start playing outgoing call dial tone
  Future<void> startOutgoingCallTone() async {
    if (!_isInitialized || _isRinging) return;

    debugPrint('🔔 Starting outgoing call tone...');

    try {
      _isRinging = true;

      // Play dial tone (shorter, less intrusive)
      await _playDialTone();

      debugPrint('✅ Outgoing call tone started');
    } catch (e) {
      debugPrint('❌ Error starting dial tone: $e');
      _isRinging = false;
    }
  }

  /// Play connection established sound
  Future<void> playConnectionSound() async {
    if (!_isInitialized) return;

    debugPrint('🔔 Playing connection sound...');

    try {
      // Play a short beep to indicate connection
      await _playShortBeep();
      debugPrint('✅ Connection sound played');
    } catch (e) {
      debugPrint('❌ Error playing connection sound: $e');
    }
  }

  /// Play call ended sound
  Future<void> playCallEndedSound() async {
    if (!_isInitialized) return;

    debugPrint('🔔 Playing call ended sound...');

    try {
      // Play a short tone to indicate call ended
      await _playEndTone();
      debugPrint('✅ Call ended sound played');
    } catch (e) {
      debugPrint('❌ Error playing call ended sound: $e');
    }
  }

  /// Stop all ringtones and audio feedback
  Future<void> stopAllSounds() async {
    if (!_isRinging) return;

    debugPrint('🔔 Stopping all ringtones and sounds...');

    try {
      _isRinging = false;

      // Stop vibration
      _stopVibration();

      // Stop audio playback
      if (_player != null && _player!.isPlaying) {
        await _player!.stopPlayer();
      }

      debugPrint('✅ All sounds stopped');
    } catch (e) {
      debugPrint('❌ Error stopping sounds: $e');
    }
  }

  /// Start vibration pattern for incoming calls
  void _startVibrationPattern() {
    _stopVibration(); // Stop any existing vibration

    if (Platform.isAndroid || Platform.isIOS) {
      // Start continuous vibration pattern
      _vibrationTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
        if (_isRinging) {
          Vibration.vibrate(duration: 1000);
        } else {
          timer.cancel();
        }
      });

      // Initial vibration
      Vibration.vibrate(duration: 1000);
    }
  }

  /// Stop vibration
  void _stopVibration() {
    _vibrationTimer?.cancel();
    _vibrationTimer = null;
    Vibration.cancel();
  }

  /// Play system ringtone
  Future<void> _playSystemRingtone() async {
    try {
      // Use system notification sound as ringtone
      // This is a simple implementation - in production you might want to use actual ringtone files
      await SystemSound.play(SystemSoundType.alert);
      
      // Schedule repeated playing
      Timer.periodic(const Duration(seconds: 3), (timer) {
        if (_isRinging) {
          SystemSound.play(SystemSoundType.alert);
        } else {
          timer.cancel();
        }
      });
    } catch (e) {
      debugPrint('❌ Error playing system ringtone: $e');
    }
  }

  /// Play dial tone for outgoing calls
  Future<void> _playDialTone() async {
    try {
      // Play a softer sound for outgoing calls
      await SystemSound.play(SystemSoundType.click);
      
      // Schedule repeated playing (less frequent than ringtone)
      Timer.periodic(const Duration(seconds: 4), (timer) {
        if (_isRinging) {
          SystemSound.play(SystemSoundType.click);
        } else {
          timer.cancel();
        }
      });
    } catch (e) {
      debugPrint('❌ Error playing dial tone: $e');
    }
  }

  /// Play short beep for connection
  Future<void> _playShortBeep() async {
    try {
      await SystemSound.play(SystemSoundType.click);
    } catch (e) {
      debugPrint('❌ Error playing short beep: $e');
    }
  }

  /// Play end tone
  Future<void> _playEndTone() async {
    try {
      await SystemSound.play(SystemSoundType.click);
    } catch (e) {
      debugPrint('❌ Error playing end tone: $e');
    }
  }

  /// Check if currently ringing
  bool get isRinging => _isRinging;

  /// Dispose the service
  Future<void> dispose() async {
    debugPrint('🔔 Disposing RingtoneService');

    await stopAllSounds();

    if (_player != null) {
      await _player!.closePlayer();
      _player = null;
    }

    _playerSubscription?.cancel();
    _isInitialized = false;
  }
}
