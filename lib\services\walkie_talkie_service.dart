import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:pulsemeet/services/enhanced_encryption_service.dart';

/// Service for real-time walkie-talkie communication within pulse groups
class WalkieTalkieService {
  static final WalkieTalkieService _instance = WalkieTalkieService._internal();
  factory WalkieTalkieService() => _instance;
  WalkieTalkieService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final EnhancedEncryptionService _encryptionService = EnhancedEncryptionService();
  
  FlutterSoundRecorder? _recorder;
  FlutterSoundPlayer? _player;
  
  bool _isInitialized = false;
  bool _isTransmitting = false;
  bool _isReceiving = false;
  String? _activeConversationId;
  
  RealtimeChannel? _walkieTalkieChannel;
  StreamSubscription<RecordingDisposition>? _recordingSubscription;
  Timer? _transmissionTimer;
  
  // Stream controllers for UI updates
  final StreamController<WalkieTalkieState> _stateController = 
      StreamController<WalkieTalkieState>.broadcast();
  final StreamController<WalkieTalkieMessage> _messageController = 
      StreamController<WalkieTalkieMessage>.broadcast();

  Stream<WalkieTalkieState> get stateStream => _stateController.stream;
  Stream<WalkieTalkieMessage> get messageStream => _messageController.stream;

  /// Initialize the walkie-talkie service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _recorder = FlutterSoundRecorder();
      _player = FlutterSoundPlayer();

      await _recorder!.openRecorder();
      await _player!.openPlayer();

      _isInitialized = true;
      debugPrint('📻 WalkieTalkieService initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing WalkieTalkieService: $e');
    }
  }

  /// Join a walkie-talkie channel for a conversation
  Future<bool> joinChannel(String conversationId) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Leave current channel if active
      await leaveChannel();

      _activeConversationId = conversationId;
      
      // Create realtime channel for walkie-talkie
      _walkieTalkieChannel = _supabase.channel('walkie_talkie_$conversationId');
      
      // Listen for incoming audio chunks
      _walkieTalkieChannel!.on(
        RealtimeListenTypes.broadcast,
        ChannelFilter(event: 'audio_chunk'),
        (payload, [ref]) => _handleIncomingAudio(payload),
      );

      // Listen for transmission start/stop events
      _walkieTalkieChannel!.on(
        RealtimeListenTypes.broadcast,
        ChannelFilter(event: 'transmission_start'),
        (payload, [ref]) => _handleTransmissionStart(payload),
      );

      _walkieTalkieChannel!.on(
        RealtimeListenTypes.broadcast,
        ChannelFilter(event: 'transmission_end'),
        (payload, [ref]) => _handleTransmissionEnd(payload),
      );

      await _walkieTalkieChannel!.subscribe();

      _updateState(WalkieTalkieState.connected);
      debugPrint('📻 Joined walkie-talkie channel: $conversationId');
      return true;
    } catch (e) {
      debugPrint('❌ Error joining walkie-talkie channel: $e');
      return false;
    }
  }

  /// Leave the current walkie-talkie channel
  Future<void> leaveChannel() async {
    try {
      await stopTransmission();
      await stopReceiving();
      
      if (_walkieTalkieChannel != null) {
        await _walkieTalkieChannel!.unsubscribe();
        _walkieTalkieChannel = null;
      }
      
      _activeConversationId = null;
      _updateState(WalkieTalkieState.disconnected);
      debugPrint('📻 Left walkie-talkie channel');
    } catch (e) {
      debugPrint('❌ Error leaving walkie-talkie channel: $e');
    }
  }

  /// Start transmitting (push-to-talk)
  Future<bool> startTransmission() async {
    if (!_isInitialized || _isTransmitting || _activeConversationId == null) {
      return false;
    }

    final hasPermission = await _checkMicrophonePermission();
    if (!hasPermission) {
      debugPrint('❌ Microphone permission denied');
      return false;
    }

    try {
      // Notify others that transmission started
      await _walkieTalkieChannel!.send(
        type: RealtimeListenTypes.broadcast,
        event: 'transmission_start',
        payload: {
          'user_id': _supabase.auth.currentUser?.id,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // Start recording with streaming
      await _recorder!.startRecorder(
        toStream: _handleAudioStream,
        codec: Codec.pcm16,
        sampleRate: 16000,
        numChannels: 1,
      );

      _isTransmitting = true;
      _updateState(WalkieTalkieState.transmitting);

      // Auto-stop after 30 seconds (safety limit)
      _transmissionTimer = Timer(const Duration(seconds: 30), () {
        stopTransmission();
      });

      debugPrint('📻 Started walkie-talkie transmission');
      return true;
    } catch (e) {
      debugPrint('❌ Error starting transmission: $e');
      return false;
    }
  }

  /// Stop transmitting
  Future<void> stopTransmission() async {
    if (!_isTransmitting) return;

    try {
      await _recorder!.stopRecorder();
      _transmissionTimer?.cancel();
      
      // Notify others that transmission ended
      if (_walkieTalkieChannel != null) {
        await _walkieTalkieChannel!.send(
          type: RealtimeListenTypes.broadcast,
          event: 'transmission_end',
          payload: {
            'user_id': _supabase.auth.currentUser?.id,
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
      }

      _isTransmitting = false;
      _updateState(_activeConversationId != null 
          ? WalkieTalkieState.connected 
          : WalkieTalkieState.disconnected);

      debugPrint('📻 Stopped walkie-talkie transmission');
    } catch (e) {
      debugPrint('❌ Error stopping transmission: $e');
    }
  }

  /// Handle audio stream from recorder
  void _handleAudioStream(Uint8List audioData) async {
    if (!_isTransmitting || _walkieTalkieChannel == null) return;

    try {
      // Encrypt audio data
      final encryptedData = await _encryptAudioData(audioData);
      
      // Send audio chunk to channel
      await _walkieTalkieChannel!.send(
        type: RealtimeListenTypes.broadcast,
        event: 'audio_chunk',
        payload: {
          'user_id': _supabase.auth.currentUser?.id,
          'audio_data': encryptedData,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('❌ Error sending audio chunk: $e');
    }
  }

  /// Handle incoming audio from other users
  void _handleIncomingAudio(Map<String, dynamic> payload) async {
    final userId = payload['user_id'] as String?;
    final currentUserId = _supabase.auth.currentUser?.id;
    
    // Don't play our own audio
    if (userId == currentUserId) return;

    try {
      final encryptedData = payload['audio_data'] as String;
      final audioData = await _decryptAudioData(encryptedData);
      
      // Play audio immediately
      await _playAudioChunk(audioData);
      
      // Notify UI
      _messageController.add(WalkieTalkieMessage(
        userId: userId!,
        timestamp: DateTime.parse(payload['timestamp'] as String),
        isReceived: true,
      ));
    } catch (e) {
      debugPrint('❌ Error handling incoming audio: $e');
    }
  }

  /// Handle transmission start from other users
  void _handleTransmissionStart(Map<String, dynamic> payload) {
    final userId = payload['user_id'] as String?;
    final currentUserId = _supabase.auth.currentUser?.id;
    
    if (userId != currentUserId) {
      _isReceiving = true;
      _updateState(WalkieTalkieState.receiving);
      
      _messageController.add(WalkieTalkieMessage(
        userId: userId!,
        timestamp: DateTime.parse(payload['timestamp'] as String),
        isTransmissionStart: true,
      ));
    }
  }

  /// Handle transmission end from other users
  void _handleTransmissionEnd(Map<String, dynamic> payload) {
    final userId = payload['user_id'] as String?;
    final currentUserId = _supabase.auth.currentUser?.id;
    
    if (userId != currentUserId) {
      _isReceiving = false;
      _updateState(WalkieTalkieState.connected);
      
      _messageController.add(WalkieTalkieMessage(
        userId: userId!,
        timestamp: DateTime.parse(payload['timestamp'] as String),
        isTransmissionEnd: true,
      ));
    }
  }

  /// Play audio chunk immediately
  Future<void> _playAudioChunk(Uint8List audioData) async {
    try {
      if (_player != null) {
        await _player!.feedFromStream(audioData);
      }
    } catch (e) {
      debugPrint('❌ Error playing audio chunk: $e');
    }
  }

  /// Stop receiving audio
  Future<void> stopReceiving() async {
    if (_isReceiving) {
      _isReceiving = false;
      if (_player != null) {
        await _player!.stopPlayer();
      }
    }
  }

  /// Encrypt audio data
  Future<String> _encryptAudioData(Uint8List audioData) async {
    try {
      final result = await _encryptionService.encryptBinary(
        audioData,
        _activeConversationId!,
      );
      return result.encryptedData.toString();
    } catch (e) {
      debugPrint('❌ Error encrypting audio data: $e');
      return audioData.toString(); // Fallback
    }
  }

  /// Decrypt audio data
  Future<Uint8List> _decryptAudioData(String encryptedData) async {
    try {
      final result = await _encryptionService.decryptBinary(
        Uint8List.fromList(encryptedData.codeUnits),
        _activeConversationId!,
      );
      return result;
    } catch (e) {
      debugPrint('❌ Error decrypting audio data: $e');
      return Uint8List.fromList(encryptedData.codeUnits); // Fallback
    }
  }

  /// Check microphone permission
  Future<bool> _checkMicrophonePermission() async {
    final status = await Permission.microphone.status;
    if (status.isGranted) return true;
    
    final result = await Permission.microphone.request();
    return result.isGranted;
  }

  /// Update state and notify listeners
  void _updateState(WalkieTalkieState state) {
    _stateController.add(state);
  }

  /// Dispose of the service
  Future<void> dispose() async {
    await leaveChannel();
    
    if (_isInitialized) {
      await _recorder?.closeRecorder();
      await _player?.closePlayer();
    }
    
    await _stateController.close();
    await _messageController.close();
  }

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isTransmitting => _isTransmitting;
  bool get isReceiving => _isReceiving;
  bool get isConnected => _activeConversationId != null;
  String? get activeConversationId => _activeConversationId;
}

/// Walkie-talkie state enumeration
enum WalkieTalkieState {
  disconnected,
  connecting,
  connected,
  transmitting,
  receiving,
  error,
}

/// Walkie-talkie message event
class WalkieTalkieMessage {
  final String userId;
  final DateTime timestamp;
  final bool isReceived;
  final bool isTransmissionStart;
  final bool isTransmissionEnd;

  WalkieTalkieMessage({
    required this.userId,
    required this.timestamp,
    this.isReceived = false,
    this.isTransmissionStart = false,
    this.isTransmissionEnd = false,
  });
}
