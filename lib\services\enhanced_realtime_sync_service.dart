import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:pulsemeet/services/conversation_service.dart';
import 'package:pulsemeet/services/pulse_group_encryption_service.dart';
import 'package:pulsemeet/models/message.dart';

/// Enhanced real-time synchronization service for pulse group messaging
/// Ensures reliable message delivery across all participants
class EnhancedRealtimeSyncService {
  static final EnhancedRealtimeSyncService _instance =
      EnhancedRealtimeSyncService._internal();
  factory EnhancedRealtimeSyncService() => _instance;
  EnhancedRealtimeSyncService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final PulseGroupEncryptionService _encryptionService =
      PulseGroupEncryptionService();

  // Real-time channels for different conversation types
  final Map<String, RealtimeChannel> _conversationChannels = {};
  final Map<String, StreamController<Message>> _messageControllers = {};
  final Map<String, List<String>> _participantCache = {};

  // Message delivery tracking
  final Map<String, Set<String>> _pendingDeliveries = {};
  final Map<String, Timer> _deliveryTimeouts = {};

  /// Initialize enhanced real-time sync for a conversation
  Future<void> initializeConversationSync(
      String conversationId, String pulseId) async {
    try {
      debugPrint(
          '🚀 Initializing enhanced real-time sync for conversation: $conversationId');

      // Clean up existing channel if any
      await _cleanupConversationSync(conversationId);

      // Create message stream controller
      _messageControllers[conversationId] =
          StreamController<Message>.broadcast();

      // Cache participants
      await _cacheParticipants(conversationId, pulseId);

      // Set up real-time channel with multiple event listeners
      await _setupRealtimeChannel(conversationId, pulseId);

      // Initialize delivery tracking for this conversation
      // Note: Individual message tracking will be set up per message

      debugPrint(
          '✅ Enhanced real-time sync initialized for conversation: $conversationId');
    } catch (e) {
      debugPrint('❌ Error initializing enhanced real-time sync: $e');
    }
  }

  /// Set up real-time channel with comprehensive event handling
  Future<void> _setupRealtimeChannel(
      String conversationId, String pulseId) async {
    try {
      final channelName = 'enhanced_sync_$conversationId';
      final channel = _supabase.channel(channelName);

      // Listen for message insertions
      channel.on(
        RealtimeListenTypes.postgresChanges,
        ChannelFilter(
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: 'conversation_id=eq.$conversationId',
        ),
        (payload, [ref]) =>
            _handleMessageInsert(payload, conversationId, pulseId),
      );

      // Listen for message updates (status changes, edits)
      channel.on(
        RealtimeListenTypes.postgresChanges,
        ChannelFilter(
          event: 'UPDATE',
          schema: 'public',
          table: 'messages',
          filter: 'conversation_id=eq.$conversationId',
        ),
        (payload, [ref]) =>
            _handleMessageUpdate(payload, conversationId, pulseId),
      );

      // Listen for participant changes
      channel.on(
        RealtimeListenTypes.postgresChanges,
        ChannelFilter(
          event: '*',
          schema: 'public',
          table: 'conversation_participants',
          filter: 'conversation_id=eq.$conversationId',
        ),
        (payload, [ref]) =>
            _handleParticipantChange(payload, conversationId, pulseId),
      );

      // Subscribe to the channel
      channel.subscribe((status, [error]) {
        if (status == 'SUBSCRIBED') {
          debugPrint('✅ Enhanced real-time channel subscribed: $channelName');
        } else if (status == 'CHANNEL_ERROR') {
          debugPrint('❌ Enhanced real-time channel error: $error');
          // Attempt to reconnect
          _attemptReconnection(conversationId, pulseId);
        }
      });

      _conversationChannels[conversationId] = channel;
    } catch (e) {
      debugPrint('❌ Error setting up real-time channel: $e');
    }
  }

  /// Handle message insertion with encryption and delivery tracking
  Future<void> _handleMessageInsert(Map<String, dynamic> payload,
      String conversationId, String pulseId) async {
    try {
      final messageData = payload['new'] as Map<String, dynamic>?;
      if (messageData == null) return;

      debugPrint('📨 Processing new message: ${messageData['id']}');

      // Create message object
      final message = Message.fromJson(messageData);

      // Decrypt if encrypted
      Message processedMessage = message;
      if (message.isEncrypted) {
        processedMessage = await _encryptionService
            .decryptMessageFromPulseGroup(message, pulseId);
        debugPrint('🔓 Message decrypted successfully');
      }

      // Track delivery to all participants
      await _trackMessageDelivery(processedMessage, conversationId);

      // Emit to stream
      _messageControllers[conversationId]?.add(processedMessage);

      // Update conversation last message timestamp
      await _updateConversationTimestamp(conversationId);

      debugPrint('✅ Message processed and delivered: ${processedMessage.id}');
    } catch (e) {
      debugPrint('❌ Error handling message insert: $e');
    }
  }

  /// Handle message updates (status changes, edits)
  Future<void> _handleMessageUpdate(Map<String, dynamic> payload,
      String conversationId, String pulseId) async {
    try {
      final messageData = payload['new'] as Map<String, dynamic>?;
      if (messageData == null) return;

      debugPrint('📝 Processing message update: ${messageData['id']}');

      final message = Message.fromJson(messageData);

      // Decrypt if encrypted
      Message processedMessage = message;
      if (message.isEncrypted) {
        processedMessage = await _encryptionService
            .decryptMessageFromPulseGroup(message, pulseId);
      }

      // Emit update to stream
      _messageControllers[conversationId]?.add(processedMessage);

      debugPrint('✅ Message update processed: ${processedMessage.id}');
    } catch (e) {
      debugPrint('❌ Error handling message update: $e');
    }
  }

  /// Handle participant changes
  Future<void> _handleParticipantChange(Map<String, dynamic> payload,
      String conversationId, String pulseId) async {
    try {
      debugPrint(
          '👥 Processing participant change for conversation: $conversationId');

      // Refresh participant cache
      await _cacheParticipants(conversationId, pulseId);

      debugPrint('✅ Participant cache refreshed');
    } catch (e) {
      debugPrint('❌ Error handling participant change: $e');
    }
  }

  /// Cache participants for delivery tracking
  Future<void> _cacheParticipants(String conversationId, String pulseId) async {
    try {
      final participants = await _supabase
          .from('conversation_participants')
          .select('user_id')
          .eq('conversation_id', conversationId);

      _participantCache[conversationId] =
          participants.map((p) => p['user_id'] as String).toList();

      debugPrint(
          '👥 Cached ${_participantCache[conversationId]?.length ?? 0} participants for conversation: $conversationId');
    } catch (e) {
      debugPrint('❌ Error caching participants: $e');
    }
  }

  /// Track message delivery to all participants
  Future<void> _trackMessageDelivery(
      Message message, String conversationId) async {
    try {
      final participants = _participantCache[conversationId] ?? [];
      if (participants.isEmpty) return;

      // Initialize pending deliveries
      _pendingDeliveries[message.id] = participants.toSet();

      // Set delivery timeout
      _deliveryTimeouts[message.id] = Timer(const Duration(seconds: 30), () {
        _handleDeliveryTimeout(message.id, conversationId);
      });

      // Mark as delivered for sender
      final currentUserId = _supabase.auth.currentUser?.id;
      if (currentUserId != null) {
        _markAsDelivered(message.id, currentUserId);
      }

      debugPrint(
          '📊 Tracking delivery for message ${message.id} to ${participants.length} participants');
    } catch (e) {
      debugPrint('❌ Error tracking message delivery: $e');
    }
  }

  /// Mark message as delivered to a participant
  void _markAsDelivered(String messageId, String userId) {
    _pendingDeliveries[messageId]?.remove(userId);

    if (_pendingDeliveries[messageId]?.isEmpty == true) {
      // All participants received the message
      _deliveryTimeouts[messageId]?.cancel();
      _pendingDeliveries.remove(messageId);
      _deliveryTimeouts.remove(messageId);
      debugPrint('✅ Message $messageId delivered to all participants');
    }
  }

  /// Handle delivery timeout
  void _handleDeliveryTimeout(String messageId, String conversationId) {
    final pendingUsers = _pendingDeliveries[messageId];
    if (pendingUsers?.isNotEmpty == true) {
      debugPrint(
          '⚠️ Message $messageId delivery timeout. Pending users: ${pendingUsers?.length}');
      // Could implement retry logic here
    }

    _pendingDeliveries.remove(messageId);
    _deliveryTimeouts.remove(messageId);
  }

  /// Update conversation last message timestamp
  Future<void> _updateConversationTimestamp(String conversationId) async {
    try {
      await _supabase
          .from('conversations')
          .update({'last_message_at': DateTime.now().toIso8601String()}).eq(
              'id', conversationId);
    } catch (e) {
      debugPrint('❌ Error updating conversation timestamp: $e');
    }
  }

  /// Attempt reconnection on channel error
  Future<void> _attemptReconnection(
      String conversationId, String pulseId) async {
    try {
      debugPrint(
          '🔄 Attempting to reconnect real-time channel for conversation: $conversationId');

      await Future.delayed(const Duration(seconds: 2));

      // Clean up and reinitialize
      await _cleanupConversationSync(conversationId);
      await initializeConversationSync(conversationId, pulseId);

      debugPrint(
          '✅ Real-time channel reconnected for conversation: $conversationId');
    } catch (e) {
      debugPrint('❌ Error during reconnection: $e');
    }
  }

  /// Get message stream for a conversation
  Stream<Message>? getMessageStream(String conversationId) {
    return _messageControllers[conversationId]?.stream;
  }

  /// Send message with enhanced delivery tracking
  Future<Message?> sendMessage(
      String conversationId, String pulseId, Message message) async {
    try {
      debugPrint('📤 Sending message with enhanced tracking: ${message.id}');

      // Encrypt message for pulse group
      final encryptedMessage = await _encryptionService
          .encryptMessageForPulseGroup(message, pulseId);

      // Insert into database
      final insertedData = await _supabase
          .from('messages')
          .insert(encryptedMessage.toJson())
          .select()
          .single();

      final insertedMessage = Message.fromJson(insertedData);

      debugPrint('✅ Message sent successfully: ${insertedMessage.id}');
      return insertedMessage;
    } catch (e) {
      debugPrint('❌ Error sending message: $e');
      return null;
    }
  }

  /// Clean up conversation sync
  Future<void> _cleanupConversationSync(String conversationId) async {
    try {
      // Unsubscribe from channel
      final channel = _conversationChannels[conversationId];
      if (channel != null) {
        await channel.unsubscribe();
        _conversationChannels.remove(conversationId);
      }

      // Close stream controller
      _messageControllers[conversationId]?.close();
      _messageControllers.remove(conversationId);

      // Clear caches
      _participantCache.remove(conversationId);

      // Clear delivery tracking
      final messageIds = _pendingDeliveries.keys.toList();
      for (final messageId in messageIds) {
        _deliveryTimeouts[messageId]?.cancel();
        _pendingDeliveries.remove(messageId);
        _deliveryTimeouts.remove(messageId);
      }

      debugPrint('🧹 Cleaned up conversation sync: $conversationId');
    } catch (e) {
      debugPrint('❌ Error cleaning up conversation sync: $e');
    }
  }

  /// Dispose of the service
  void dispose() {
    final conversationIds = _conversationChannels.keys.toList();
    for (final conversationId in conversationIds) {
      _cleanupConversationSync(conversationId);
    }
  }
}
