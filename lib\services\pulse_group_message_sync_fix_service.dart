import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:pulsemeet/services/conversation_service.dart';
import 'package:pulsemeet/services/automatic_conversation_service.dart';
import 'package:pulsemeet/services/pulse_group_realtime_diagnostic_service.dart';
import 'package:pulsemeet/services/enhanced_realtime_sync_service.dart';
import 'package:pulsemeet/models/message.dart';

/// Comprehensive service to diagnose and fix pulse group message synchronization issues
class PulseGroupMessageSyncFixService {
  static final PulseGroupMessageSyncFixService _instance =
      PulseGroupMessageSyncFixService._internal();
  factory PulseGroupMessageSyncFixService() => _instance;
  PulseGroupMessageSyncFixService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final ConversationService _conversationService = ConversationService();
  final AutomaticConversationService _autoConversationService =
      AutomaticConversationService();
  final PulseGroupRealtimeDiagnosticService _diagnosticService =
      PulseGroupRealtimeDiagnosticService();
  final EnhancedRealtimeSyncService _enhancedSync =
      EnhancedRealtimeSyncService();

  /// Run comprehensive diagnosis and fix for pulse group message sync issues
  Future<SyncFixResults> diagnoseAndFixPulseGroupSync(String pulseId) async {
    final results = SyncFixResults();

    try {
      debugPrint(
          '🔧 Starting comprehensive pulse group message sync diagnosis and fix for pulse: $pulseId');

      // Step 1: Run diagnostic
      results.diagnosticResults =
          await _diagnosticService.runComprehensiveDiagnostic(pulseId);

      // Step 2: Fix conversation setup if needed
      if (results.diagnosticResults != null &&
          !results.diagnosticResults!.conversationCheck) {
        results.conversationFixed = await _fixConversationSetup(pulseId);
      } else {
        results.conversationFixed = true;
      }

      // Step 3: Fix participant sync if needed
      results.participantsFixed = await _fixParticipantSync(pulseId);

      // Step 4: Fix real-time subscriptions
      results.realtimeFixed = await _fixRealtimeSubscriptions(pulseId);

      // Step 5: Test message delivery
      results.messageDeliveryFixed = await _testAndFixMessageDelivery(pulseId);

      // Step 6: Initialize enhanced sync
      results.enhancedSyncInitialized = await _initializeEnhancedSync(pulseId);

      // Step 7: Run final verification
      results.finalVerification = await _runFinalVerification(pulseId);

      debugPrint('🔧 Pulse group message sync diagnosis and fix completed');
      debugPrint('📊 Fix Results: ${results.getSuccessRate()}% success rate');

      return results;
    } catch (e) {
      debugPrint('❌ Error during pulse group sync diagnosis and fix: $e');
      return results;
    }
  }

  /// Fix conversation setup for pulse group
  Future<bool> _fixConversationSetup(String pulseId) async {
    try {
      debugPrint('🔧 Fixing conversation setup for pulse: $pulseId');

      // Check if conversation exists
      final existingConversation =
          await _autoConversationService.getPulseConversation(pulseId);

      if (existingConversation == null) {
        debugPrint('🆕 Creating new pulse group conversation');

        // Create conversation using RPC function
        final conversationId =
            await _supabase.rpc('create_pulse_conversation', params: {
          'pulse_id_param': pulseId,
        });

        if (conversationId != null) {
          debugPrint('✅ Pulse group conversation created: $conversationId');
          return true;
        } else {
          debugPrint('❌ Failed to create pulse group conversation');
          return false;
        }
      } else {
        debugPrint(
            '✅ Pulse group conversation already exists: ${existingConversation.id}');
        return true;
      }
    } catch (e) {
      debugPrint('❌ Error fixing conversation setup: $e');
      return false;
    }
  }

  /// Fix participant synchronization
  Future<bool> _fixParticipantSync(String pulseId) async {
    try {
      debugPrint('🔧 Fixing participant sync for pulse: $pulseId');

      // Use RPC function to sync participants
      final result =
          await _supabase.rpc('sync_pulse_conversation_participants', params: {
        'pulse_id_param': pulseId,
      });

      if (result == true) {
        debugPrint('✅ Participant sync completed successfully');
        return true;
      } else {
        debugPrint('❌ Participant sync failed');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error fixing participant sync: $e');
      return false;
    }
  }

  /// Fix real-time subscriptions
  Future<bool> _fixRealtimeSubscriptions(String pulseId) async {
    try {
      debugPrint('🔧 Fixing real-time subscriptions for pulse: $pulseId');

      final conversation =
          await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for real-time subscription fix');
        return false;
      }

      // Subscribe to messages for this conversation
      await _conversationService.subscribeToMessages(conversation.id);

      // Initialize enhanced real-time sync
      await _enhancedSync.initializeConversationSync(conversation.id, pulseId);

      debugPrint('✅ Real-time subscriptions fixed');
      return true;
    } catch (e) {
      debugPrint('❌ Error fixing real-time subscriptions: $e');
      return false;
    }
  }

  /// Test and fix message delivery
  Future<bool> _testAndFixMessageDelivery(String pulseId) async {
    try {
      debugPrint('🔧 Testing and fixing message delivery for pulse: $pulseId');

      final conversation =
          await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for message delivery test');
        return false;
      }

      // Send a test message
      final testMessage =
          'Sync test message ${DateTime.now().millisecondsSinceEpoch}';
      final sentMessage = await _conversationService.sendTextMessage(
          conversation.id, testMessage);

      if (sentMessage != null) {
        debugPrint('✅ Test message sent successfully');

        // Wait for real-time delivery
        await Future.delayed(const Duration(seconds: 3));

        // Check if message appears in database
        final messages = await _supabase
            .from('messages')
            .select()
            .eq('conversation_id', conversation.id)
            .eq('content', testMessage)
            .limit(1);

        if (messages.isNotEmpty) {
          debugPrint('✅ Test message found in database - delivery working');
          return true;
        } else {
          debugPrint('❌ Test message not found in database - delivery issue');
          return false;
        }
      } else {
        debugPrint('❌ Failed to send test message');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error testing message delivery: $e');
      return false;
    }
  }

  /// Initialize enhanced sync
  Future<bool> _initializeEnhancedSync(String pulseId) async {
    try {
      debugPrint('🔧 Initializing enhanced sync for pulse: $pulseId');

      final conversation =
          await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for enhanced sync initialization');
        return false;
      }

      await _enhancedSync.initializeConversationSync(conversation.id, pulseId);

      debugPrint('✅ Enhanced sync initialized successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Error initializing enhanced sync: $e');
      return false;
    }
  }

  /// Run final verification
  Future<bool> _runFinalVerification(String pulseId) async {
    try {
      debugPrint('🔧 Running final verification for pulse: $pulseId');

      // Run diagnostic again to verify fixes
      final finalDiagnostic =
          await _diagnosticService.runComprehensiveDiagnostic(pulseId);

      if (finalDiagnostic.allTestsPassed) {
        debugPrint('✅ Final verification passed - all systems working');
        return true;
      } else {
        debugPrint('⚠️ Final verification shows remaining issues');
        debugPrint(
            '📊 Final diagnostic success rate: ${finalDiagnostic.getSuccessRate()}%');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error during final verification: $e');
      return false;
    }
  }

  /// Generate comprehensive fix report
  String generateFixReport(SyncFixResults results) {
    final buffer = StringBuffer();
    buffer.writeln('🔧 PULSE GROUP MESSAGE SYNC FIX REPORT');
    buffer.writeln('=====================================');
    buffer.writeln('');
    buffer.writeln('📊 Overall Fix Success Rate: ${results.getSuccessRate()}%');
    buffer.writeln('');
    buffer.writeln('📋 Fix Results:');
    buffer.writeln(
        '• Conversation Setup: ${results.conversationFixed ? '✅ FIXED' : '❌ FAILED'}');
    buffer.writeln(
        '• Participant Sync: ${results.participantsFixed ? '✅ FIXED' : '❌ FAILED'}');
    buffer.writeln(
        '• Real-time Subscriptions: ${results.realtimeFixed ? '✅ FIXED' : '❌ FAILED'}');
    buffer.writeln(
        '• Message Delivery: ${results.messageDeliveryFixed ? '✅ FIXED' : '❌ FAILED'}');
    buffer.writeln(
        '• Enhanced Sync: ${results.enhancedSyncInitialized ? '✅ INITIALIZED' : '❌ FAILED'}');
    buffer.writeln(
        '• Final Verification: ${results.finalVerification ? '✅ PASSED' : '❌ FAILED'}');
    buffer.writeln('');

    if (results.diagnosticResults != null) {
      buffer.writeln('📋 Initial Diagnostic Results:');
      buffer.writeln(
          '• Conversation Check: ${results.diagnosticResults!.conversationCheck ? '✅ PASS' : '❌ FAIL'}');
      buffer.writeln(
          '• Message Insertion: ${results.diagnosticResults!.messageInsertion ? '✅ PASS' : '❌ FAIL'}');
      buffer.writeln(
          '• Real-time Subscription: ${results.diagnosticResults!.realtimeSubscription ? '✅ PASS' : '❌ FAIL'}');
      buffer.writeln(
          '• Encryption Test: ${results.diagnosticResults!.encryptionTest ? '✅ PASS' : '❌ FAIL'}');
      buffer.writeln(
          '• Participant Permissions: ${results.diagnosticResults!.participantPermissions ? '✅ PASS' : '❌ FAIL'}');
      buffer.writeln(
          '• Cross-device Delivery: ${results.diagnosticResults!.crossDeviceDelivery ? '✅ PASS' : '❌ FAIL'}');
      buffer.writeln('');
    }

    buffer.writeln('🎯 Status:');
    if (results.allFixesSuccessful) {
      buffer.writeln(
          '✅ All fixes applied successfully! Pulse group messaging should now work correctly.');
    } else {
      buffer.writeln(
          '⚠️ Some fixes failed. Manual intervention may be required.');
      buffer.writeln('');
      buffer.writeln('🔧 Next Steps:');
      if (!results.conversationFixed) {
        buffer.writeln('• Check pulse group conversation creation permissions');
      }
      if (!results.participantsFixed) {
        buffer.writeln(
            '• Verify pulse participant data and conversation participant sync');
      }
      if (!results.realtimeFixed) {
        buffer.writeln(
            '• Check Supabase real-time configuration and network connectivity');
      }
      if (!results.messageDeliveryFixed) {
        buffer.writeln(
            '• Verify message insertion permissions and encryption setup');
      }
      if (!results.enhancedSyncInitialized) {
        buffer
            .writeln('• Check enhanced real-time sync service initialization');
      }
    }

    return buffer.toString();
  }
}

/// Results container for sync fix operations
class SyncFixResults {
  DiagnosticResults? diagnosticResults;
  bool conversationFixed = false;
  bool participantsFixed = false;
  bool realtimeFixed = false;
  bool messageDeliveryFixed = false;
  bool enhancedSyncInitialized = false;
  bool finalVerification = false;

  int getSuccessCount() {
    int count = 0;
    if (conversationFixed) count++;
    if (participantsFixed) count++;
    if (realtimeFixed) count++;
    if (messageDeliveryFixed) count++;
    if (enhancedSyncInitialized) count++;
    if (finalVerification) count++;
    return count;
  }

  double getSuccessRate() {
    return (getSuccessCount() / 6.0 * 100).roundToDouble();
  }

  bool get allFixesSuccessful => getSuccessCount() == 6;
}
