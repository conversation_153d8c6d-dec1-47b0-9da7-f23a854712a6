import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:pulsemeet/models/conversation.dart';
import 'package:pulsemeet/models/pulse.dart';
import 'package:pulsemeet/services/conversation_service.dart';
import 'package:pulsemeet/services/supabase_service.dart';
import 'package:pulsemeet/screens/chat/chat_screen.dart';
import 'package:pulsemeet/widgets/avatar.dart';

/// Helper class to combine conversation with pulse data
class ConversationWithPulse {
  final Conversation conversation;
  final Pulse pulse;

  ConversationWithPulse({
    required this.conversation,
    required this.pulse,
  });
}

/// Screen showing list of all pulse group chats
class ChatListScreen extends StatefulWidget {
  const ChatListScreen({super.key});

  @override
  State<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends State<ChatListScreen> {
  final ConversationService _conversationService = ConversationService();
  List<ConversationWithPulse> _conversations = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadConversations();
  }

  Future<void> _loadConversations() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final supabaseService =
          Provider.of<SupabaseService>(context, listen: false);

      // Listen to conversations stream for real-time updates
      _conversationService.conversationsStream.listen((conversations) async {
        final pulseConversations = conversations
            .where((c) => c.type == ConversationType.pulseGroup)
            .toList();

        // Get pulse details for each conversation
        final conversationsWithPulse = <ConversationWithPulse>[];

        for (final conversation in pulseConversations) {
          if (conversation.pulseId != null) {
            try {
              final pulse =
                  await supabaseService.getPulseById(conversation.pulseId!);
              if (pulse != null) {
                conversationsWithPulse.add(ConversationWithPulse(
                  conversation: conversation,
                  pulse: pulse,
                ));
              }
            } catch (e) {
              debugPrint(
                  'Error loading pulse for conversation ${conversation.id}: $e');
            }
          }
        }

        // Sort by last message time
        conversationsWithPulse.sort((a, b) {
          final aTime =
              a.conversation.lastMessageAt ?? a.conversation.createdAt;
          final bTime =
              b.conversation.lastMessageAt ?? b.conversation.createdAt;
          return bTime.compareTo(aTime);
        });

        if (mounted) {
          setState(() {
            _conversations = conversationsWithPulse;
            _isLoading = false;
          });
        }
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load conversations: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF121212) : Colors.white,
      appBar: AppBar(
        title: const Text('Pulse Chats'),
        backgroundColor: isDark ? const Color(0xFF121212) : Colors.white,
        foregroundColor: isDark ? Colors.white : Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadConversations,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadConversations,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_conversations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No pulse chats yet',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Join a pulse to start chatting with other participants',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadConversations,
      child: ListView.builder(
        itemCount: _conversations.length,
        itemBuilder: (context, index) {
          final conversationWithPulse = _conversations[index];
          return _buildConversationTile(conversationWithPulse);
        },
      ),
    );
  }

  Widget _buildConversationTile(ConversationWithPulse conversationWithPulse) {
    final conversation = conversationWithPulse.conversation;
    final pulse = conversationWithPulse.pulse;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    final lastMessageTime =
        conversation.lastMessageAt ?? conversation.createdAt;
    final timeAgo = timeago.format(lastMessageTime);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: CircleAvatar(
          radius: 24,
          backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
          child: Text(
            pulse.activityEmoji ?? '🎯',
            style: const TextStyle(fontSize: 20),
          ),
        ),
        title: Text(
          pulse.title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
            color: isDark ? Colors.white : Colors.black87,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              conversation.lastMessagePreview ?? 'No messages yet',
              style: TextStyle(
                color: isDark ? Colors.grey[400] : Colors.grey[600],
                fontSize: 14,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              timeAgo,
              style: TextStyle(
                color: isDark ? Colors.grey[500] : Colors.grey[500],
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (conversation.unreadCount != null &&
                conversation.unreadCount! > 0)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  conversation.unreadCount! > 99
                      ? '99+'
                      : conversation.unreadCount.toString(),
                  style: TextStyle(
                    color: theme.colorScheme.onPrimary,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            const SizedBox(height: 4),
            Icon(
              Icons.chevron_right,
              color: isDark ? Colors.grey[600] : Colors.grey[400],
              size: 20,
            ),
          ],
        ),
        onTap: () => _openChat(conversation),
      ),
    );
  }

  void _openChat(Conversation conversation) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(conversation: conversation),
      ),
    );
  }
}
