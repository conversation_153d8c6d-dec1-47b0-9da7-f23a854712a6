import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:pulsemeet/models/call.dart';
import 'package:pulsemeet/services/call_manager_service.dart';
import 'package:pulsemeet/widgets/avatar.dart';

/// Screen for handling incoming calls
class IncomingCallScreen extends StatefulWidget {
  final Call call;

  const IncomingCallScreen({
    super.key,
    required this.call,
  });

  @override
  State<IncomingCallScreen> createState() => _IncomingCallScreenState();
}

class _IncomingCallScreenState extends State<IncomingCallScreen>
    with TickerProviderStateMixin {
  final CallManagerService _callManager = CallManagerService();

  late AnimationController _pulseController;
  late AnimationController _slideController;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isAnswering = false;
  bool _isDeclining = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startRinging();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    // Pulse animation for the avatar
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Slide animation for the screen
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOut,
    ));

    // Start animations
    _pulseController.repeat(reverse: true);
    _slideController.forward();
  }

  void _startRinging() {
    // Add haptic feedback
    HapticFeedback.vibrate();

    // TODO: Play ringtone
  }

  Future<void> _answerCall() async {
    if (_isAnswering) return;

    setState(() {
      _isAnswering = true;
    });

    try {
      await _callManager.answerCall(widget.call.id);

      if (mounted) {
        // Navigate to active call screen
        Navigator.of(context).pushReplacementNamed(
          '/active_call',
          arguments: widget.call,
        );
      }
    } catch (e) {
      debugPrint('❌ Error answering call: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to answer call: $e'),
            backgroundColor: Colors.red,
          ),
        );
        Navigator.of(context).pop();
      }
    }
  }

  Future<void> _declineCall() async {
    if (_isDeclining) return;

    setState(() {
      _isDeclining = true;
    });

    try {
      await _callManager.declineCall(widget.call.id);

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('❌ Error declining call: $e');

      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Get caller info
    final caller = widget.call.participants.firstWhere(
      (p) => p.userId == widget.call.callerId,
      orElse: () => CallParticipant(
        userId: widget.call.callerId,
        name: 'Unknown',
        joinedAt: DateTime.now(),
      ),
    );

    return Scaffold(
      backgroundColor: isDark ? Colors.black : const Color(0xFF1E1E1E),
      body: SlideTransition(
        position: _slideAnimation,
        child: SafeArea(
          child: Column(
            children: [
              // Header with call type
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Icon(
                      widget.call.type == CallType.video
                          ? Icons.videocam
                          : Icons.call,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      widget.call.type == CallType.video
                          ? 'Incoming video call'
                          : 'Incoming call',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),

              // Spacer
              const Spacer(),

              // Caller avatar with pulse animation
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 4,
                        ),
                      ),
                      child: ClipOval(
                        child: UserAvatar(
                          userId: caller.userId,
                          displayName: caller.name,
                          avatarUrl: caller.avatarUrl,
                          size: 192,
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 32),

              // Caller name
              Text(
                caller.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 32,
                  fontWeight: FontWeight.w300,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 8),

              // Call type indicator
              Text(
                widget.call.type == CallType.video
                    ? 'Video call'
                    : 'Voice call',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 18,
                ),
              ),

              // Spacer
              const Spacer(),

              // Action buttons
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Decline button
                    GestureDetector(
                      onTap: _isDeclining ? null : _declineCall,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: _isDeclining
                            ? const CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              )
                            : const Icon(
                                Icons.call_end,
                                color: Colors.white,
                                size: 36,
                              ),
                      ),
                    ),

                    // Answer button
                    GestureDetector(
                      onTap: _isAnswering ? null : _answerCall,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                        child: _isAnswering
                            ? const CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              )
                            : Icon(
                                widget.call.type == CallType.video
                                    ? Icons.videocam
                                    : Icons.call,
                                color: Colors.white,
                                size: 36,
                              ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 60),
            ],
          ),
        ),
      ),
    );
  }
}
