import 'package:flutter/material.dart';
import 'package:pulsemeet/services/pulse_group_message_sync_fix_service.dart';
import 'package:pulsemeet/services/pulse_group_realtime_diagnostic_service.dart';
import 'package:pulsemeet/services/conversation_service.dart';
import 'package:pulsemeet/services/automatic_conversation_service.dart';

/// Test widget to demonstrate and verify pulse group messaging functionality
class PulseGroupMessageTestWidget extends StatefulWidget {
  final String pulseId;

  const PulseGroupMessageTestWidget({
    Key? key,
    required this.pulseId,
  }) : super(key: key);

  @override
  State<PulseGroupMessageTestWidget> createState() => _PulseGroupMessageTestWidgetState();
}

class _PulseGroupMessageTestWidgetState extends State<PulseGroupMessageTestWidget> {
  final PulseGroupMessageSyncFixService _fixService = PulseGroupMessageSyncFixService();
  final PulseGroupRealtimeDiagnosticService _diagnosticService = PulseGroupRealtimeDiagnosticService();
  final ConversationService _conversationService = ConversationService();
  final AutomaticConversationService _autoConversationService = AutomaticConversationService();
  
  final TextEditingController _messageController = TextEditingController();
  
  bool _isRunningDiagnostic = false;
  bool _isRunningFix = false;
  bool _isSendingMessage = false;
  
  String _diagnosticReport = '';
  String _fixReport = '';
  String _lastSentMessage = '';
  
  DiagnosticResults? _lastDiagnosticResults;
  SyncFixResults? _lastFixResults;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Pulse Group Message Test'),
        backgroundColor: Colors.blue,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Pulse Info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Pulse Group Testing',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text('Pulse ID: ${widget.pulseId}'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Diagnostic Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Diagnostic Tests',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    
                    ElevatedButton(
                      onPressed: _isRunningDiagnostic ? null : _runDiagnostic,
                      child: _isRunningDiagnostic
                          ? const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                ),
                                SizedBox(width: 8),
                                Text('Running Diagnostic...'),
                              ],
                            )
                          : const Text('Run Comprehensive Diagnostic'),
                    ),
                    
                    if (_lastDiagnosticResults != null) ...[
                      const SizedBox(height: 16),
                      _buildDiagnosticResults(),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Fix Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Automatic Fix',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    
                    ElevatedButton(
                      onPressed: _isRunningFix ? null : _runFix,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                      ),
                      child: _isRunningFix
                          ? const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                ),
                                SizedBox(width: 8),
                                Text('Running Fix...'),
                              ],
                            )
                          : const Text('Run Automatic Fix'),
                    ),
                    
                    if (_lastFixResults != null) ...[
                      const SizedBox(height: 16),
                      _buildFixResults(),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Message Test Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Message Test',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    
                    TextField(
                      controller: _messageController,
                      decoration: const InputDecoration(
                        labelText: 'Test Message',
                        hintText: 'Enter a test message to send...',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    ElevatedButton(
                      onPressed: _isSendingMessage ? null : _sendTestMessage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                      ),
                      child: _isSendingMessage
                          ? const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                ),
                                SizedBox(width: 8),
                                Text('Sending...'),
                              ],
                            )
                          : const Text('Send Test Message'),
                    ),
                    
                    if (_lastSentMessage.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          border: Border.all(color: Colors.green),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Last Sent Message:',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 4),
                            Text(_lastSentMessage),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Reports Section
            if (_diagnosticReport.isNotEmpty || _fixReport.isNotEmpty) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Detailed Reports',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      
                      if (_diagnosticReport.isNotEmpty) ...[
                        ExpansionTile(
                          title: const Text('Diagnostic Report'),
                          children: [
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.grey.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                _diagnosticReport,
                                style: const TextStyle(fontFamily: 'monospace'),
                              ),
                            ),
                          ],
                        ),
                      ],
                      
                      if (_fixReport.isNotEmpty) ...[
                        ExpansionTile(
                          title: const Text('Fix Report'),
                          children: [
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.grey.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                _fixReport,
                                style: const TextStyle(fontFamily: 'monospace'),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDiagnosticResults() {
    final results = _lastDiagnosticResults!;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: results.allTestsPassed 
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.orange.withValues(alpha: 0.1),
        border: Border.all(
          color: results.allTestsPassed ? Colors.green : Colors.orange,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Diagnostic Results: ${results.getSuccessRate()}% Success',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          _buildTestResult('Conversation Setup', results.conversationCheck),
          _buildTestResult('Message Insertion', results.messageInsertion),
          _buildTestResult('Real-time Subscription', results.realtimeSubscription),
          _buildTestResult('Encryption Test', results.encryptionTest),
          _buildTestResult('Participant Permissions', results.participantPermissions),
          _buildTestResult('Cross-device Delivery', results.crossDeviceDelivery),
        ],
      ),
    );
  }

  Widget _buildFixResults() {
    final results = _lastFixResults!;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: results.allFixesSuccessful 
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        border: Border.all(
          color: results.allFixesSuccessful ? Colors.green : Colors.red,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Fix Results: ${results.getSuccessRate()}% Success',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          _buildTestResult('Conversation Fixed', results.conversationFixed),
          _buildTestResult('Participants Fixed', results.participantsFixed),
          _buildTestResult('Real-time Fixed', results.realtimeFixed),
          _buildTestResult('Message Delivery Fixed', results.messageDeliveryFixed),
          _buildTestResult('Enhanced Sync Initialized', results.enhancedSyncInitialized),
          _buildTestResult('Final Verification', results.finalVerification),
        ],
      ),
    );
  }

  Widget _buildTestResult(String testName, bool passed) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            passed ? Icons.check_circle : Icons.error,
            color: passed ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(testName),
        ],
      ),
    );
  }

  Future<void> _runDiagnostic() async {
    setState(() {
      _isRunningDiagnostic = true;
    });

    try {
      final results = await _diagnosticService.runComprehensiveDiagnostic(widget.pulseId);
      final report = _diagnosticService.generateDiagnosticReport(results);

      setState(() {
        _lastDiagnosticResults = results;
        _diagnosticReport = report;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Diagnostic completed: ${results.getSuccessRate()}% success'),
          backgroundColor: results.allTestsPassed ? Colors.green : Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Diagnostic failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isRunningDiagnostic = false;
      });
    }
  }

  Future<void> _runFix() async {
    setState(() {
      _isRunningFix = true;
    });

    try {
      final results = await _fixService.diagnoseAndFixPulseGroupSync(widget.pulseId);
      final report = _fixService.generateFixReport(results);

      setState(() {
        _lastFixResults = results;
        _fixReport = report;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Fix completed: ${results.getSuccessRate()}% success'),
          backgroundColor: results.allFixesSuccessful ? Colors.green : Colors.red,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Fix failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isRunningFix = false;
      });
    }
  }

  Future<void> _sendTestMessage() async {
    if (_messageController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a test message'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isSendingMessage = true;
    });

    try {
      final conversation = await _autoConversationService.getPulseConversation(widget.pulseId);
      
      if (conversation == null) {
        throw Exception('No conversation found for pulse');
      }

      final message = await _conversationService.sendTextMessage(
        conversation.id,
        _messageController.text.trim(),
      );

      if (message != null) {
        setState(() {
          _lastSentMessage = 'Message sent successfully: ${message.content}';
        });
        _messageController.clear();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Test message sent successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        throw Exception('Failed to send message');
      }
    } catch (e) {
      setState(() {
        _lastSentMessage = 'Failed to send message: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to send message: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isSendingMessage = false;
      });
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }
}
