import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';

import 'package:pulsemeet/models/call.dart';
import 'package:pulsemeet/services/call_manager_service.dart';
import 'package:pulsemeet/screens/call/incoming_call_screen.dart';

/// Service for managing call overlays and navigation
class CallOverlayService {
  static final CallOverlayService _instance = CallOverlayService._internal();
  factory CallOverlayService() => _instance;
  CallOverlayService._internal();

  final CallManagerService _callManager = CallManagerService();

  // Navigation context
  BuildContext? _context;

  // Stream subscriptions
  StreamSubscription<Call?>? _callStateSubscription;

  // Current call state
  Call? _currentCall;
  bool _isShowingIncomingCall = false;
  bool _isInitialized = false;

  /// Initialize the overlay service
  Future<void> initialize(BuildContext context) async {
    if (_isInitialized) {
      debugPrint('⚠️ CallOverlayService already initialized');
      return;
    }

    debugPrint('📞 Initializing CallOverlayService...');

    _context = context;

    try {
      // Initialize call manager
      await _callManager.initialize();

      // Setup stream subscriptions
      _setupStreamSubscriptions();

      _isInitialized = true;
      debugPrint('✅ CallOverlayService initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing CallOverlayService: $e');
      rethrow;
    }
  }

  /// Setup stream subscriptions
  void _setupStreamSubscriptions() {
    debugPrint('📞 Setting up call overlay stream subscriptions');

    // Listen for call state changes
    _callStateSubscription = _callManager.callStateStream.listen(
      (call) {
        debugPrint('📞 Call state changed: ${call?.state}');
        _handleCallStateChange(call);
      },
      onError: (error) {
        debugPrint('❌ Error in call state stream: $error');
      },
    );
  }

  /// Handle call state changes
  void _handleCallStateChange(Call? call) {
    _currentCall = call;

    if (call == null) {
      // No active call
      _dismissIncomingCallIfShowing();
      return;
    }

    switch (call.state) {
      case CallState.incoming:
        _showIncomingCall(call);
        break;
      case CallState.connecting:
      case CallState.connected:
        _dismissIncomingCallIfShowing();
        _navigateToActiveCall(call);
        break;
      case CallState.ended:
      case CallState.declined:
      case CallState.missed:
      case CallState.failed:
        _dismissIncomingCallIfShowing();
        break;
      default:
        break;
    }
  }

  /// Show incoming call screen
  void _showIncomingCall(Call call) {
    if (_isShowingIncomingCall || _context == null) {
      debugPrint('⚠️ Already showing incoming call or no context available');
      return;
    }

    debugPrint('📞 Showing incoming call screen for: ${call.id}');

    _isShowingIncomingCall = true;

    // Navigate to incoming call screen
    Navigator.of(_context!)
        .push(
      MaterialPageRoute(
        builder: (context) => IncomingCallScreen(call: call),
        fullscreenDialog: true,
      ),
    )
        .then((_) {
      _isShowingIncomingCall = false;
    });
  }

  /// Navigate to active call screen
  void _navigateToActiveCall(Call call) {
    if (_context == null) {
      debugPrint('⚠️ No context available for navigation');
      return;
    }

    debugPrint('📞 Navigating to active call screen for: ${call.id}');

    // Navigate to active call screen
    Navigator.of(_context!).pushNamed(
      '/active_call',
      arguments: call,
    );
  }

  /// Dismiss incoming call screen if showing
  void _dismissIncomingCallIfShowing() {
    if (_isShowingIncomingCall && _context != null) {
      debugPrint('📞 Dismissing incoming call screen');
      Navigator.of(_context!).pop();
      _isShowingIncomingCall = false;
    }
  }

  /// Update context (useful when navigating between screens)
  void updateContext(BuildContext context) {
    _context = context;
  }

  /// Check if there's an active call
  bool get hasActiveCall => _callManager.hasActiveCall;

  /// Get current call
  Call? get currentCall => _currentCall;

  /// Start a call
  Future<void> startCall({
    required String conversationId,
    required String calleeId,
    required CallType type,
  }) async {
    await _callManager.startCall(
      conversationId: conversationId,
      calleeId: calleeId,
      type: type,
    );
  }

  /// Answer the current incoming call
  Future<void> answerCall() async {
    if (_currentCall != null) {
      await _callManager.answerCall(_currentCall!.id);
    }
  }

  /// Decline the current incoming call
  Future<void> declineCall() async {
    if (_currentCall != null) {
      await _callManager.declineCall(_currentCall!.id);
    }
  }

  /// End the current call
  Future<void> endCall() async {
    await _callManager.endCall();
  }

  /// Toggle microphone
  Future<void> toggleMicrophone() async {
    await _callManager.toggleMicrophone();
  }

  /// Toggle speaker
  Future<void> toggleSpeaker() async {
    await _callManager.toggleSpeaker();
  }

  /// Toggle camera (for video calls)
  Future<void> toggleCamera() async {
    await _callManager.toggleCamera();
  }

  /// Switch camera (front/back)
  Future<void> switchCamera() async {
    await _callManager.switchCamera();
  }

  /// Get call state stream
  Stream<Call?> get callStateStream => _callManager.callStateStream;

  /// Get local stream
  Stream<MediaStream?> get localStreamStream => _callManager.localStreamStream;

  /// Get remote stream
  Stream<MediaStream?> get remoteStreamStream =>
      _callManager.remoteStreamStream;

  /// Dispose the service
  void dispose() {
    debugPrint('📞 Disposing CallOverlayService');

    // Cancel subscriptions
    _callStateSubscription?.cancel();

    // Clear context
    _context = null;

    // Dispose call manager
    _callManager.dispose();

    _isInitialized = false;
  }
}
