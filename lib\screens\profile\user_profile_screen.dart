import 'package:flutter/material.dart';
import 'package:provider/provider.dart' as provider_pkg;
import 'package:pulsemeet/models/profile.dart';
import 'package:pulsemeet/models/pulse.dart';
import 'package:pulsemeet/services/supabase_service.dart';
import 'package:pulsemeet/widgets/profile/profile_header.dart';
import 'package:pulsemeet/widgets/profile/profile_info_section.dart';
import 'package:pulsemeet/widgets/profile/profile_rating_section.dart';
import 'package:pulsemeet/widgets/profile/profile_stats_section.dart';
import 'package:pulsemeet/widgets/pulse_card.dart';

/// Screen for viewing user profiles (simplified - no connections/direct messaging/rating)
class UserProfileScreen extends StatefulWidget {
  final String userId;

  const UserProfileScreen({
    super.key,
    required this.userId,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  Profile? _profile;
  List<Pulse> _userPulses = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchProfile();
    _fetchUserPulses();
  }

  /// Fetch user profile
  Future<void> _fetchProfile() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final supabaseService = provider_pkg.Provider.of<SupabaseService>(context, listen: false);
      final profile = await supabaseService.getProfile(widget.userId);

      if (mounted) {
        setState(() {
          _profile = profile;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Error loading profile: ${e.toString()}';
        });
      }
    }
  }

  /// Fetch pulses created by this user
  Future<void> _fetchUserPulses() async {
    try {
      final supabaseService = provider_pkg.Provider.of<SupabaseService>(context, listen: false);
      final pulses = await supabaseService.getPulsesByCreator(widget.userId);

      if (mounted) {
        setState(() {
          _userPulses = pulses;
        });
      }
    } catch (e) {
      debugPrint('Error fetching user pulses: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_profile?.displayName ?? 'User Profile'),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchProfile,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_profile == null) {
      return const Center(
        child: Text('User not found'),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await _fetchProfile();
        await _fetchUserPulses();
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Profile header with avatar and name
            ProfileHeader(profile: _profile!),

            const SizedBox(height: 16),

            // Profile stats
            ProfileStatsSection(
              pulsesCreated: _userPulses.length,
              pulsesJoined: 0, // We don't track this data
              connections: 0, // Connections feature removed
              onPulsesCreatedTap: () {
                // Already showing pulses below
              },
              onPulsesJoinedTap: () {
                // Not implemented
              },
              onConnectionsTap: () {
                // Connections feature removed
              },
            ),

            const SizedBox(height: 16),

            // Profile info
            ProfileInfoSection(
              profile: _profile!,
              onEditTap: null, // Can't edit another user's profile
            ),

            const SizedBox(height: 16),

            // Rating section (view only)
            ProfileRatingSection(
              profile: _profile!,
              showDetailedBreakdown: true,
              showRatings: true,
              maxRatingsToShow: 2,
              onViewAllTap: () {
                // Navigate to ratings screen if needed
                Navigator.pushNamed(
                  context,
                  '/ratings',
                  arguments: _profile,
                );
              },
            ),

            const SizedBox(height: 24),

            // Pulses created by this user
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Pulses Created',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  if (_userPulses.isEmpty)
                    const Padding(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      child: Center(
                        child: Text('No pulses created yet'),
                      ),
                    )
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _userPulses.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: PulseCard(
                            pulse: _userPulses[index],
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
