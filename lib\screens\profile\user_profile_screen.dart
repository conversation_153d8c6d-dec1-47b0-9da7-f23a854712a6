import 'package:flutter/material.dart';
import 'package:provider/provider.dart' as provider_pkg;
import 'package:pulsemeet/models/profile.dart';
import 'package:pulsemeet/models/pulse.dart';
import 'package:pulsemeet/models/rating.dart';
import 'package:pulsemeet/services/supabase_service.dart';
import 'package:pulsemeet/services/rating_service.dart';
import 'package:pulsemeet/screens/profile/ratings_screen.dart';
import 'package:pulsemeet/widgets/profile/profile_header.dart';
import 'package:pulsemeet/widgets/profile/profile_info_section.dart';
import 'package:pulsemeet/widgets/profile/profile_rating_section.dart';
import 'package:pulsemeet/widgets/profile/profile_stats_section.dart';
import 'package:pulsemeet/widgets/pulse_card.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Screen for viewing user profiles (simplified - no connections/direct messaging)
class UserProfileScreen extends StatefulWidget {
  final String userId;

  const UserProfileScreen({
    super.key,
    required this.userId,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  final _ratingService = RatingService();

  Profile? _profile;
  List<Pulse> _userPulses = [];
  bool _isLoading = true;
  String? _errorMessage;
  bool _canRateUser = false;

  @override
  void initState() {
    super.initState();
    _fetchProfile();
    _fetchUserPulses();
    _checkIfCanRateUser();
  }

  /// Fetch user profile
  Future<void> _fetchProfile() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final supabaseService = provider_pkg.Provider.of<SupabaseService>(context, listen: false);
      final profile = await supabaseService.getProfile(widget.userId);

      if (mounted) {
        setState(() {
          _profile = profile;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Error loading profile: ${e.toString()}';
        });
      }
    }
  }

  /// Fetch pulses created by this user
  Future<void> _fetchUserPulses() async {
    try {
      final supabaseService = provider_pkg.Provider.of<SupabaseService>(context, listen: false);
      final pulses = await supabaseService.getUserPulses(widget.userId);

      if (mounted) {
        setState(() {
          _userPulses = pulses;
        });
      }
    } catch (e) {
      debugPrint('Error fetching user pulses: $e');
    }
  }

  /// Check if the current user can rate this user
  Future<void> _checkIfCanRateUser() async {
    try {
      final currentUserId = Supabase.instance.client.auth.currentUser?.id;
      if (currentUserId == null || currentUserId == widget.userId) {
        setState(() {
          _canRateUser = false;
        });
        return;
      }

      // Check if user has participated in pulses with this user
      final canRate = await _ratingService.canRateUser(currentUserId, widget.userId);
      
      if (mounted) {
        setState(() {
          _canRateUser = canRate;
        });
      }
    } catch (e) {
      debugPrint('Error checking if can rate user: $e');
      setState(() {
        _canRateUser = false;
      });
    }
  }

  /// Show rating dialog
  Future<void> _showRatingDialog() async {
    final rating = await showDialog<int>(
      context: context,
      builder: (context) => _RatingDialog(),
    );

    if (rating != null) {
      await _submitRating(rating);
    }
  }

  /// Submit rating
  Future<void> _submitRating(int rating) async {
    try {
      final currentUserId = Supabase.instance.client.auth.currentUser?.id;
      if (currentUserId == null) return;

      await _ratingService.submitRating(
        raterId: currentUserId,
        ratedUserId: widget.userId,
        rating: rating,
        comment: '', // Could add comment field to dialog
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Rating submitted successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh profile to show updated rating
        await _fetchProfile();
        setState(() {
          _canRateUser = false; // Can only rate once
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error submitting rating: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Navigate to ratings screen
  void _navigateToRatingsScreen() {
    if (_profile != null) {
      Navigator.pushNamed(
        context,
        '/ratings',
        arguments: _profile,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_profile?.displayName ?? 'User Profile'),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchProfile,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_profile == null) {
      return const Center(
        child: Text('User not found'),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await _fetchProfile();
        await _fetchUserPulses();
        await _checkIfCanRateUser();
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Profile header with avatar and name
            ProfileHeader(profile: _profile!),

            const SizedBox(height: 16),

            // Rate user button (if applicable)
            if (_canRateUser && widget.userId != Supabase.instance.client.auth.currentUser?.id)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: OutlinedButton.icon(
                  onPressed: _showRatingDialog,
                  icon: const Icon(Icons.star_outline),
                  label: const Text('Rate User'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                ),
              ),

            // Profile stats
            ProfileStatsSection(
              pulsesCreated: _userPulses.length,
              pulsesJoined: 0, // We don't track this data
              connections: 0, // Connections feature removed
              onPulsesCreatedTap: () {
                // Already showing pulses below
              },
              onPulsesJoinedTap: () {
                // Not implemented
              },
              onConnectionsTap: () {
                // Connections feature removed
              },
            ),

            const SizedBox(height: 16),

            // Profile info
            ProfileInfoSection(
              profile: _profile!,
              onEditTap: null, // Can't edit another user's profile
            ),

            const SizedBox(height: 16),

            // Rating section
            ProfileRatingSection(
              profile: _profile!,
              showDetailedBreakdown: true,
              showRatings: true,
              maxRatingsToShow: 2,
              onViewAllTap: _navigateToRatingsScreen,
            ),

            const SizedBox(height: 24),

            // Pulses created by this user
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Pulses Created',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  if (_userPulses.isEmpty)
                    const Padding(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      child: Center(
                        child: Text('No pulses created yet'),
                      ),
                    )
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _userPulses.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: PulseCard(
                            pulse: _userPulses[index],
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}

/// Simple rating dialog
class _RatingDialog extends StatefulWidget {
  @override
  State<_RatingDialog> createState() => _RatingDialogState();
}

class _RatingDialogState extends State<_RatingDialog> {
  int _selectedRating = 5;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Rate User'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('How would you rate this user?'),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(5, (index) {
              final rating = index + 1;
              return IconButton(
                onPressed: () {
                  setState(() {
                    _selectedRating = rating;
                  });
                },
                icon: Icon(
                  rating <= _selectedRating ? Icons.star : Icons.star_border,
                  color: Colors.amber,
                  size: 32,
                ),
              );
            }),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(_selectedRating),
          child: const Text('Submit'),
        ),
      ],
    );
  }
}
