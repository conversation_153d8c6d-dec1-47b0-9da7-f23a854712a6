import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart' as provider;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

import 'package:pulsemeet/models/call.dart';
import 'package:pulsemeet/config/supabase_config.dart';
import 'package:pulsemeet/services/call_manager_service.dart';
import 'package:pulsemeet/main.dart';

/// Service for handling call signaling through Supabase real-time channels
class CallSignalingService {
  static final CallSignalingService _instance =
      CallSignalingService._internal();
  factory CallSignalingService() => _instance;
  CallSignalingService._internal();

  final SupabaseClient _supabase = SupabaseConfig.client;
  final Uuid _uuid = const Uuid();

  // Signaling channels
  final Map<String, RealtimeChannel> _channels = {};

  // Stream controllers for different signaling events
  final StreamController<Call> _incomingCallController =
      StreamController<Call>.broadcast();
  final StreamController<SignalingData> _signalingDataController =
      StreamController<SignalingData>.broadcast();
  final StreamController<String> _callEndedController =
      StreamController<String>.broadcast();
  final StreamController<String> _callDeclinedController =
      StreamController<String>.broadcast();

  // Getters for streams
  Stream<Call> get incomingCallStream => _incomingCallController.stream;
  Stream<SignalingData> get signalingDataStream =>
      _signalingDataController.stream;
  Stream<String> get callEndedStream => _callEndedController.stream;
  Stream<String> get callDeclinedStream => _callDeclinedController.stream;

  /// Initialize the signaling service
  Future<void> initialize() async {
    debugPrint('📡 Initializing CallSignalingService...');

    try {
      // Setup global incoming call listener
      await _setupIncomingCallListener();

      debugPrint('✅ CallSignalingService initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing CallSignalingService: $e');
      rethrow;
    }
  }

  /// Setup listener for incoming calls
  Future<void> _setupIncomingCallListener() async {
    final currentUserId = _supabase.auth.currentUser?.id;
    if (currentUserId == null) {
      debugPrint(
          '⚠️ User not authenticated, skipping incoming call listener setup');
      return;
    }

    final channelName = 'incoming_calls_$currentUserId';
    debugPrint('📞 ========== SETTING UP INCOMING CALL LISTENER ==========');
    debugPrint('📞 Current user ID: $currentUserId');
    debugPrint('📞 Listening channel name: $channelName');
    debugPrint('📞 Supabase client: ${_supabase.toString()}');

    try {
      final channel = _supabase.channel(channelName);
      debugPrint('📞 Channel created: ${channel.toString()}');

      // Listen for call invitations
      debugPrint('📞 Setting up call_invitation event listener...');
      channel.on(
        RealtimeListenTypes.broadcast,
        ChannelFilter(event: 'call_invitation'),
        (payload, [ref]) {
          debugPrint('📞 ========== INCOMING CALL DETECTED! ==========');
          debugPrint('📞 Channel: $channelName');
          debugPrint('📞 Event: call_invitation');
          debugPrint('📞 Raw payload: $payload');
          debugPrint('📞 Ref: $ref');
          debugPrint('📞 Timestamp: ${DateTime.now().toIso8601String()}');
          debugPrint('📞 About to call _handleIncomingCall...');
          _handleIncomingCall(payload);
        },
      );

      // Listen for call state updates
      debugPrint('📞 Setting up call_state_update event listener...');
      channel.on(
        RealtimeListenTypes.broadcast,
        ChannelFilter(event: 'call_state_update'),
        (payload, [ref]) {
          debugPrint('📞 Call state update received: $payload');
          _handleCallStateUpdate(payload);
        },
      );

      // Add a generic listener to catch ALL broadcast events
      debugPrint('📞 Setting up generic broadcast listener for debugging...');
      channel.on(
        RealtimeListenTypes.broadcast,
        ChannelFilter(event: '*'),
        (payload, [ref]) {
          debugPrint('📞 *** GENERIC BROADCAST EVENT DETECTED ***');
          debugPrint('📞 Channel: $channelName');
          debugPrint('📞 Payload: $payload');
          debugPrint('📞 Ref: $ref');
        },
      );

      debugPrint('📞 Subscribing to channel...');
      channel.subscribe();
      debugPrint('📞 Channel subscribed successfully');

      _channels['incoming_calls'] = channel;

      debugPrint(
          '✅ Incoming call listener setup complete for channel: $channelName');
      debugPrint('📞 ========== INCOMING CALL LISTENER READY ==========');
    } catch (e) {
      debugPrint('❌ Error setting up incoming call listener: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  /// Setup signaling channel for a specific call
  Future<void> setupCallSignaling(String callId) async {
    debugPrint('📡 Setting up signaling for call: $callId');

    try {
      final channel = _supabase.channel('call_signaling_$callId');

      // Listen for WebRTC signaling data
      channel.on(
        RealtimeListenTypes.broadcast,
        ChannelFilter(event: 'webrtc_signaling'),
        (payload, [ref]) {
          _handleWebRTCSignaling(payload);
        },
      );

      // Listen for call control events
      channel.on(
        RealtimeListenTypes.broadcast,
        ChannelFilter(event: 'call_control'),
        (payload, [ref]) {
          _handleCallControl(payload);
        },
      );

      channel.subscribe();
      _channels[callId] = channel;

      debugPrint('✅ Call signaling setup complete for: $callId');
    } catch (e) {
      debugPrint('❌ Error setting up call signaling: $e');
      rethrow;
    }
  }

  /// Send call invitation to the callee
  Future<void> sendCallInvitation({
    required Call call,
    required String calleeName,
    String? calleeAvatarUrl,
  }) async {
    debugPrint('📞 ========== SENDING CALL INVITATION ==========');
    debugPrint('📞 Call ID: ${call.id}');
    debugPrint('📞 Caller ID: ${call.callerId}');
    debugPrint('📞 Callee ID: ${call.calleeId}');
    debugPrint('📞 Call Type: ${call.type.name}');

    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('📞 Current user authenticated: ${currentUser.id}');

      // CRITICAL: Save call to database first
      await _saveCallToDatabase(call);

      // Get caller info
      final callerName = currentUser.userMetadata?['full_name'] ??
          currentUser.userMetadata?['name'] ??
          'Unknown';
      final callerAvatarUrl = currentUser.userMetadata?['avatar_url'];

      debugPrint('📞 Caller info: $callerName');

      // CRITICAL FIX: Create and subscribe to the channel before sending
      final channelName = 'incoming_calls_${call.calleeId}';
      debugPrint('📞 Target channel name: $channelName');

      final channel = _supabase.channel(channelName);

      // Subscribe to the channel first
      debugPrint('📞 Subscribing to channel...');
      channel.subscribe();

      // Wait a moment for subscription to be established
      await Future.delayed(const Duration(milliseconds: 500));
      debugPrint('📞 Channel subscription delay completed');

      // Prepare payload
      final payload = {
        'call_id': call.id,
        'conversation_id': call.conversationId,
        'call_type': call.type.name,
        'caller_id': call.callerId,
        'caller_name': callerName,
        'caller_avatar_url': callerAvatarUrl,
        'callee_id': call.calleeId,
        'callee_name': calleeName,
        'callee_avatar_url': calleeAvatarUrl,
        'created_at': call.createdAt.toIso8601String(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      debugPrint('📞 Payload prepared: $payload');

      // Now send the invitation
      debugPrint('📞 Sending call invitation to channel: $channelName');
      await channel.send(
        type: RealtimeListenTypes.broadcast,
        event: 'call_invitation',
        payload: payload,
      );

      // Store the channel for cleanup later
      _channels['invitation_${call.id}'] = channel;

      debugPrint(
          '✅ Call invitation sent successfully to channel: $channelName');
      debugPrint('📞 ========== CALL INVITATION SENT ==========');
    } catch (e) {
      debugPrint('❌ Error sending call invitation: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  /// Send call answer
  Future<void> sendCallAnswer(String callId) async {
    debugPrint('📞 Sending call answer for: $callId');

    try {
      final channel = _channels[callId];
      if (channel == null) {
        throw Exception('Signaling channel not found for call: $callId');
      }

      await channel.send(
        type: RealtimeListenTypes.broadcast,
        event: 'call_control',
        payload: {
          'action': 'answer',
          'call_id': callId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      debugPrint('✅ Call answer sent successfully');
    } catch (e) {
      debugPrint('❌ Error sending call answer: $e');
      rethrow;
    }
  }

  /// Send call decline
  Future<void> sendCallDecline(String callId) async {
    debugPrint('📞 Sending call decline for: $callId');

    try {
      // Send through the call's signaling channel
      final channel = _channels[callId];
      if (channel != null) {
        await channel.send(
          type: RealtimeListenTypes.broadcast,
          event: 'call_control',
          payload: {
            'action': 'decline',
            'call_id': callId,
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
      }

      debugPrint('✅ Call decline sent successfully');
    } catch (e) {
      debugPrint('❌ Error sending call decline: $e');
      rethrow;
    }
  }

  /// Send call end signal
  Future<void> sendCallEnd(String callId, CallEndReason reason) async {
    debugPrint('📞 Sending call end for: $callId with reason: ${reason.name}');

    try {
      final channel = _channels[callId];
      if (channel != null) {
        await channel.send(
          type: RealtimeListenTypes.broadcast,
          event: 'call_control',
          payload: {
            'action': 'end',
            'call_id': callId,
            'reason': reason.name,
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
      }

      debugPrint('✅ Call end signal sent successfully');
    } catch (e) {
      debugPrint('❌ Error sending call end signal: $e');
      rethrow;
    }
  }

  /// Send WebRTC signaling data (offer, answer, ICE candidates)
  Future<void> sendSignalingData({
    required String callId,
    required SignalingData data,
  }) async {
    debugPrint('📡 Sending signaling data: ${data.type}');

    try {
      final channel = _channels[callId];
      if (channel == null) {
        throw Exception('Signaling channel not found for call: $callId');
      }

      await channel.send(
        type: RealtimeListenTypes.broadcast,
        event: 'webrtc_signaling',
        payload: {
          'call_id': callId,
          'signaling_data': data.toJson(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      debugPrint('✅ Signaling data sent successfully');
    } catch (e) {
      debugPrint('❌ Error sending signaling data: $e');
      rethrow;
    }
  }

  /// Handle incoming call invitation
  void _handleIncomingCall(Map<String, dynamic> payload) {
    debugPrint('📞 ========== HANDLING INCOMING CALL ==========');
    debugPrint('📞 Payload keys: ${payload.keys.toList()}');
    debugPrint('📞 Full payload: $payload');

    try {
      // Validate required fields
      final callId = payload['call_id'];
      final conversationId = payload['conversation_id'];
      final callType = payload['call_type'];
      final callerId = payload['caller_id'];
      final calleeId = payload['callee_id'];
      final callerName = payload['caller_name'];
      final createdAt = payload['created_at'];

      debugPrint('📞 Extracted fields:');
      debugPrint('📞   Call ID: $callId');
      debugPrint('📞   Conversation ID: $conversationId');
      debugPrint('📞   Call Type: $callType');
      debugPrint('📞   Caller ID: $callerId');
      debugPrint('📞   Callee ID: $calleeId');
      debugPrint('📞   Caller Name: $callerName');
      debugPrint('📞   Created At: $createdAt');

      if (callId == null || callerId == null || calleeId == null) {
        debugPrint('❌ Missing required fields in payload');
        return;
      }

      final call = Call(
        id: callId,
        conversationId: conversationId,
        type: callType == 'video' ? CallType.video : CallType.voice,
        state: CallState.incoming,
        callerId: callerId,
        calleeId: calleeId,
        participants: [
          CallParticipant(
            userId: callerId,
            name: callerName ?? 'Unknown',
            avatarUrl: payload['caller_avatar_url'],
            joinedAt: DateTime.now(),
          ),
          CallParticipant(
            userId: calleeId,
            name: payload['callee_name'] ?? 'Unknown',
            avatarUrl: payload['callee_avatar_url'],
            joinedAt: DateTime.now(),
          ),
        ],
        createdAt:
            createdAt != null ? DateTime.parse(createdAt) : DateTime.now(),
      );

      debugPrint('📞 Call object created: ${call.toString()}');

      // Update call state in database to 'incoming'
      debugPrint('📞 Updating call state in database...');
      _updateCallInDatabase(call.id, CallState.incoming);

      debugPrint('📞 Adding call to incoming call stream...');
      _incomingCallController.add(call);

      debugPrint('✅ Incoming call processed successfully');
      debugPrint('📞 ========== INCOMING CALL HANDLED ==========');
    } catch (e) {
      debugPrint('❌ Error handling incoming call: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
    }
  }

  /// Handle call state updates
  void _handleCallStateUpdate(Map<String, dynamic> payload) {
    debugPrint('📞 Handling call state update: ${payload['action']}');

    // Implementation will be expanded based on needs
  }

  /// Handle WebRTC signaling data
  void _handleWebRTCSignaling(Map<String, dynamic> payload) {
    debugPrint('📡 Handling WebRTC signaling data');

    try {
      final signalingData = SignalingData.fromJson(payload['signaling_data']);
      _signalingDataController.add(signalingData);
      debugPrint('✅ WebRTC signaling data processed successfully');
    } catch (e) {
      debugPrint('❌ Error handling WebRTC signaling: $e');
    }
  }

  /// Handle call control events
  void _handleCallControl(Map<String, dynamic> payload) {
    debugPrint('📞 Handling call control: ${payload['action']}');

    try {
      final action = payload['action'];
      final callId = payload['call_id'];

      switch (action) {
        case 'answer':
          // Call was answered by the other party
          _updateCallInDatabase(callId, CallState.connecting,
              answerTime: DateTime.now());
          break;
        case 'decline':
          _updateCallInDatabase(callId, CallState.declined,
              endTime: DateTime.now(), endReason: CallEndReason.declined);
          _callDeclinedController.add(callId);
          break;
        case 'end':
          _updateCallInDatabase(callId, CallState.ended,
              endTime: DateTime.now(), endReason: CallEndReason.userEnded);
          _callEndedController.add(callId);
          break;
        default:
          debugPrint('⚠️ Unknown call control action: $action');
      }

      debugPrint('✅ Call control processed successfully');
    } catch (e) {
      debugPrint('❌ Error handling call control: $e');
    }
  }

  /// Close signaling channel for a call
  Future<void> closeCallSignaling(String callId) async {
    debugPrint('📡 Closing signaling for call: $callId');

    try {
      final channel = _channels[callId];
      if (channel != null) {
        await channel.unsubscribe();
        _channels.remove(callId);
      }

      debugPrint('✅ Call signaling closed successfully');
    } catch (e) {
      debugPrint('❌ Error closing call signaling: $e');
    }
  }

  /// Save call to database
  Future<void> _saveCallToDatabase(Call call) async {
    debugPrint('💾 Saving call to database: ${call.id}');

    try {
      await _supabase.from('calls').insert({
        'id': call.id,
        'conversation_id': call.conversationId,
        'caller_id': call.callerId,
        'callee_id': call.calleeId,
        'call_type': call.type.name,
        'call_state': call.state.name,
        'start_time': call.createdAt.toIso8601String(),
        'created_at': call.createdAt.toIso8601String(),
      });

      debugPrint('✅ Call saved to database successfully');
    } catch (e) {
      debugPrint('❌ Error saving call to database: $e');
      // Don't rethrow - call can continue without database persistence
    }
  }

  /// Update call state in database
  Future<void> _updateCallInDatabase(
    String callId,
    CallState state, {
    DateTime? answerTime,
    DateTime? endTime,
    int? durationSeconds,
    CallEndReason? endReason,
  }) async {
    debugPrint('💾 Updating call in database: $callId -> ${state.name}');

    try {
      final updateData = <String, dynamic>{
        'call_state': state.name,
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (answerTime != null) {
        updateData['answer_time'] = answerTime.toIso8601String();
      }
      if (endTime != null) {
        updateData['end_time'] = endTime.toIso8601String();
      }
      if (durationSeconds != null) {
        updateData['duration_seconds'] = durationSeconds;
      }
      if (endReason != null) {
        updateData['end_reason'] = endReason.name;
      }

      await _supabase.from('calls').update(updateData).eq('id', callId);

      debugPrint('✅ Call updated in database successfully');
    } catch (e) {
      debugPrint('❌ Error updating call in database: $e');
      // Don't rethrow - call can continue without database persistence
    }
  }

  /// Test method to simulate incoming call (for debugging)
  Future<void> testIncomingCall() async {
    debugPrint('🧪 ========== TESTING INCOMING CALL SIMULATION ==========');

    // Step 0: Clear any existing call state first
    debugPrint('🧪 Step 0: Clearing any existing call state...');
    try {
      // Get CallManagerService from the provider tree
      final context = navigatorKey.currentContext;
      if (context != null) {
        final callManager =
            provider.Provider.of<CallManagerService>(context, listen: false);
        callManager.resetCallState();
        debugPrint('✅ Call state cleared successfully');
      } else {
        debugPrint('⚠️ No context available to clear call state');
      }
    } catch (e) {
      debugPrint('⚠️ Error clearing call state: $e');
    }

    // Wait a moment for state to clear
    await Future.delayed(const Duration(milliseconds: 500));

    debugPrint('🧪 Step 1: Checking authentication...');

    final currentUserId = _supabase.auth.currentUser?.id;
    if (currentUserId == null) {
      debugPrint('❌ Cannot test - user not authenticated');
      return;
    }
    debugPrint('✅ User authenticated: $currentUserId');

    debugPrint('🧪 Step 2: Creating test call payload...');
    // Create a test call payload
    final testPayload = {
      'call_id': 'test-call-${DateTime.now().millisecondsSinceEpoch}',
      'conversation_id': 'test-conversation',
      'call_type': 'voice',
      'caller_id': 'test-caller-id',
      'caller_name': 'Test Caller',
      'caller_avatar_url': null,
      'callee_id': currentUserId,
      'callee_name': 'Test Callee',
      'callee_avatar_url': null,
      'created_at': DateTime.now().toIso8601String(),
      'timestamp': DateTime.now().toIso8601String(),
    };

    debugPrint('✅ Test payload created: $testPayload');

    debugPrint('🧪 Step 3: Checking stream controller status...');
    debugPrint(
        '🧪 Incoming call controller closed: ${_incomingCallController.isClosed}');
    debugPrint(
        '🧪 Incoming call controller has listeners: ${_incomingCallController.hasListener}');

    debugPrint(
        '🧪 Step 4: Simulating incoming call by calling _handleIncomingCall...');

    // Directly call the handler to test the flow
    _handleIncomingCall(testPayload);

    debugPrint('🧪 Step 5: Waiting 2 seconds to observe results...');
    await Future.delayed(const Duration(seconds: 2));

    debugPrint('🧪 ========== INCOMING CALL SIMULATION COMPLETE ==========');
    debugPrint(
        '🧪 Expected result: IncomingCallScreen should appear on device');
    debugPrint('🧪 If no screen appears, check the logs above for errors');
  }

  /// Dispose the service
  void dispose() {
    debugPrint('📡 Disposing CallSignalingService');

    // Close all channels
    for (final channel in _channels.values) {
      channel.unsubscribe();
    }
    _channels.clear();

    // Close stream controllers
    _incomingCallController.close();
    _signalingDataController.close();
    _callEndedController.close();
    _callDeclinedController.close();
  }
}
