import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

import 'package:pulsemeet/models/call.dart';
import 'package:pulsemeet/config/supabase_config.dart';

/// Service for handling call signaling through Supabase real-time channels
class CallSignalingService {
  static final CallSignalingService _instance =
      CallSignalingService._internal();
  factory CallSignalingService() => _instance;
  CallSignalingService._internal();

  final SupabaseClient _supabase = SupabaseConfig.client;
  final Uuid _uuid = const Uuid();

  // Signaling channels
  final Map<String, RealtimeChannel> _channels = {};

  // Stream controllers for different signaling events
  final StreamController<Call> _incomingCallController =
      StreamController<Call>.broadcast();
  final StreamController<SignalingData> _signalingDataController =
      StreamController<SignalingData>.broadcast();
  final StreamController<String> _callEndedController =
      StreamController<String>.broadcast();
  final StreamController<String> _callDeclinedController =
      StreamController<String>.broadcast();

  // Getters for streams
  Stream<Call> get incomingCallStream => _incomingCallController.stream;
  Stream<SignalingData> get signalingDataStream =>
      _signalingDataController.stream;
  Stream<String> get callEndedStream => _callEndedController.stream;
  Stream<String> get callDeclinedStream => _callDeclinedController.stream;

  /// Initialize the signaling service
  Future<void> initialize() async {
    debugPrint('📡 Initializing CallSignalingService...');

    try {
      // Setup global incoming call listener
      await _setupIncomingCallListener();

      debugPrint('✅ CallSignalingService initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing CallSignalingService: $e');
      rethrow;
    }
  }

  /// Setup listener for incoming calls
  Future<void> _setupIncomingCallListener() async {
    final currentUserId = _supabase.auth.currentUser?.id;
    if (currentUserId == null) {
      debugPrint(
          '⚠️ User not authenticated, skipping incoming call listener setup');
      return;
    }

    final channelName = 'incoming_calls_$currentUserId';
    debugPrint('📞 Setting up incoming call listener for user: $currentUserId');
    debugPrint('📞 Channel name: $channelName');

    try {
      final channel = _supabase.channel(channelName);

      // Listen for call invitations
      channel.on(
        RealtimeListenTypes.broadcast,
        ChannelFilter(event: 'call_invitation'),
        (payload, [ref]) {
          debugPrint('📞 INCOMING CALL DETECTED! Channel: $channelName');
          debugPrint('📞 Raw payload: $payload');
          _handleIncomingCall(payload);
        },
      );

      // Listen for call state updates
      channel.on(
        RealtimeListenTypes.broadcast,
        ChannelFilter(event: 'call_state_update'),
        (payload, [ref]) {
          debugPrint('📞 Call state update received: $payload');
          _handleCallStateUpdate(payload);
        },
      );

      channel.subscribe();
      debugPrint('📞 Channel subscribed successfully');
      _channels['incoming_calls'] = channel;

      debugPrint(
          '✅ Incoming call listener setup complete for channel: $channelName');
    } catch (e) {
      debugPrint('❌ Error setting up incoming call listener: $e');
      rethrow;
    }
  }

  /// Setup signaling channel for a specific call
  Future<void> setupCallSignaling(String callId) async {
    debugPrint('📡 Setting up signaling for call: $callId');

    try {
      final channel = _supabase.channel('call_signaling_$callId');

      // Listen for WebRTC signaling data
      channel.on(
        RealtimeListenTypes.broadcast,
        ChannelFilter(event: 'webrtc_signaling'),
        (payload, [ref]) {
          _handleWebRTCSignaling(payload);
        },
      );

      // Listen for call control events
      channel.on(
        RealtimeListenTypes.broadcast,
        ChannelFilter(event: 'call_control'),
        (payload, [ref]) {
          _handleCallControl(payload);
        },
      );

      channel.subscribe();
      _channels[callId] = channel;

      debugPrint('✅ Call signaling setup complete for: $callId');
    } catch (e) {
      debugPrint('❌ Error setting up call signaling: $e');
      rethrow;
    }
  }

  /// Send call invitation to the callee
  Future<void> sendCallInvitation({
    required Call call,
    required String calleeName,
    String? calleeAvatarUrl,
  }) async {
    debugPrint('📞 Sending call invitation for call: ${call.id}');

    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // CRITICAL: Save call to database first
      await _saveCallToDatabase(call);

      // Get caller info
      final callerName = currentUser.userMetadata?['full_name'] ??
          currentUser.userMetadata?['name'] ??
          'Unknown';
      final callerAvatarUrl = currentUser.userMetadata?['avatar_url'];

      // CRITICAL FIX: Create and subscribe to the channel before sending
      final channelName = 'incoming_calls_${call.calleeId}';
      final channel = _supabase.channel(channelName);

      // Subscribe to the channel first
      channel.subscribe();

      // Wait a moment for subscription to be established
      await Future.delayed(const Duration(milliseconds: 200));

      // Now send the invitation
      await channel.send(
        type: RealtimeListenTypes.broadcast,
        event: 'call_invitation',
        payload: {
          'call_id': call.id,
          'conversation_id': call.conversationId,
          'call_type': call.type.name,
          'caller_id': call.callerId,
          'caller_name': callerName,
          'caller_avatar_url': callerAvatarUrl,
          'callee_id': call.calleeId,
          'callee_name': calleeName,
          'callee_avatar_url': calleeAvatarUrl,
          'created_at': call.createdAt.toIso8601String(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // Store the channel for cleanup later
      _channels['invitation_${call.id}'] = channel;

      debugPrint(
          '✅ Call invitation sent successfully to channel: $channelName');
    } catch (e) {
      debugPrint('❌ Error sending call invitation: $e');
      rethrow;
    }
  }

  /// Send call answer
  Future<void> sendCallAnswer(String callId) async {
    debugPrint('📞 Sending call answer for: $callId');

    try {
      final channel = _channels[callId];
      if (channel == null) {
        throw Exception('Signaling channel not found for call: $callId');
      }

      await channel.send(
        type: RealtimeListenTypes.broadcast,
        event: 'call_control',
        payload: {
          'action': 'answer',
          'call_id': callId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      debugPrint('✅ Call answer sent successfully');
    } catch (e) {
      debugPrint('❌ Error sending call answer: $e');
      rethrow;
    }
  }

  /// Send call decline
  Future<void> sendCallDecline(String callId) async {
    debugPrint('📞 Sending call decline for: $callId');

    try {
      // Send through the call's signaling channel
      final channel = _channels[callId];
      if (channel != null) {
        await channel.send(
          type: RealtimeListenTypes.broadcast,
          event: 'call_control',
          payload: {
            'action': 'decline',
            'call_id': callId,
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
      }

      debugPrint('✅ Call decline sent successfully');
    } catch (e) {
      debugPrint('❌ Error sending call decline: $e');
      rethrow;
    }
  }

  /// Send call end signal
  Future<void> sendCallEnd(String callId, CallEndReason reason) async {
    debugPrint('📞 Sending call end for: $callId with reason: ${reason.name}');

    try {
      final channel = _channels[callId];
      if (channel != null) {
        await channel.send(
          type: RealtimeListenTypes.broadcast,
          event: 'call_control',
          payload: {
            'action': 'end',
            'call_id': callId,
            'reason': reason.name,
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
      }

      debugPrint('✅ Call end signal sent successfully');
    } catch (e) {
      debugPrint('❌ Error sending call end signal: $e');
      rethrow;
    }
  }

  /// Send WebRTC signaling data (offer, answer, ICE candidates)
  Future<void> sendSignalingData({
    required String callId,
    required SignalingData data,
  }) async {
    debugPrint('📡 Sending signaling data: ${data.type}');

    try {
      final channel = _channels[callId];
      if (channel == null) {
        throw Exception('Signaling channel not found for call: $callId');
      }

      await channel.send(
        type: RealtimeListenTypes.broadcast,
        event: 'webrtc_signaling',
        payload: {
          'call_id': callId,
          'signaling_data': data.toJson(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      debugPrint('✅ Signaling data sent successfully');
    } catch (e) {
      debugPrint('❌ Error sending signaling data: $e');
      rethrow;
    }
  }

  /// Handle incoming call invitation
  void _handleIncomingCall(Map<String, dynamic> payload) {
    debugPrint('📞 Handling incoming call invitation');
    debugPrint('📞 Payload: $payload');

    try {
      final call = Call(
        id: payload['call_id'],
        conversationId: payload['conversation_id'],
        type: payload['call_type'] == 'video' ? CallType.video : CallType.voice,
        state: CallState.incoming,
        callerId: payload['caller_id'],
        calleeId: payload['callee_id'],
        participants: [
          CallParticipant(
            userId: payload['caller_id'],
            name: payload['caller_name'] ?? 'Unknown',
            avatarUrl: payload['caller_avatar_url'],
            joinedAt: DateTime.now(),
          ),
          CallParticipant(
            userId: payload['callee_id'],
            name: payload['callee_name'] ?? 'Unknown',
            avatarUrl: payload['callee_avatar_url'],
            joinedAt: DateTime.now(),
          ),
        ],
        createdAt: DateTime.parse(payload['created_at']),
      );

      // Update call state in database to 'incoming'
      _updateCallInDatabase(call.id, CallState.incoming);

      _incomingCallController.add(call);
      debugPrint('✅ Incoming call processed successfully');
    } catch (e) {
      debugPrint('❌ Error handling incoming call: $e');
    }
  }

  /// Handle call state updates
  void _handleCallStateUpdate(Map<String, dynamic> payload) {
    debugPrint('📞 Handling call state update: ${payload['action']}');

    // Implementation will be expanded based on needs
  }

  /// Handle WebRTC signaling data
  void _handleWebRTCSignaling(Map<String, dynamic> payload) {
    debugPrint('📡 Handling WebRTC signaling data');

    try {
      final signalingData = SignalingData.fromJson(payload['signaling_data']);
      _signalingDataController.add(signalingData);
      debugPrint('✅ WebRTC signaling data processed successfully');
    } catch (e) {
      debugPrint('❌ Error handling WebRTC signaling: $e');
    }
  }

  /// Handle call control events
  void _handleCallControl(Map<String, dynamic> payload) {
    debugPrint('📞 Handling call control: ${payload['action']}');

    try {
      final action = payload['action'];
      final callId = payload['call_id'];

      switch (action) {
        case 'answer':
          // Call was answered by the other party
          _updateCallInDatabase(callId, CallState.connecting,
              answerTime: DateTime.now());
          break;
        case 'decline':
          _updateCallInDatabase(callId, CallState.declined,
              endTime: DateTime.now(), endReason: CallEndReason.declined);
          _callDeclinedController.add(callId);
          break;
        case 'end':
          _updateCallInDatabase(callId, CallState.ended,
              endTime: DateTime.now(), endReason: CallEndReason.userEnded);
          _callEndedController.add(callId);
          break;
        default:
          debugPrint('⚠️ Unknown call control action: $action');
      }

      debugPrint('✅ Call control processed successfully');
    } catch (e) {
      debugPrint('❌ Error handling call control: $e');
    }
  }

  /// Close signaling channel for a call
  Future<void> closeCallSignaling(String callId) async {
    debugPrint('📡 Closing signaling for call: $callId');

    try {
      final channel = _channels[callId];
      if (channel != null) {
        await channel.unsubscribe();
        _channels.remove(callId);
      }

      debugPrint('✅ Call signaling closed successfully');
    } catch (e) {
      debugPrint('❌ Error closing call signaling: $e');
    }
  }

  /// Save call to database
  Future<void> _saveCallToDatabase(Call call) async {
    debugPrint('💾 Saving call to database: ${call.id}');

    try {
      await _supabase.from('calls').insert({
        'id': call.id,
        'conversation_id': call.conversationId,
        'caller_id': call.callerId,
        'callee_id': call.calleeId,
        'call_type': call.type.name,
        'call_state': call.state.name,
        'start_time': call.createdAt.toIso8601String(),
        'created_at': call.createdAt.toIso8601String(),
      });

      debugPrint('✅ Call saved to database successfully');
    } catch (e) {
      debugPrint('❌ Error saving call to database: $e');
      // Don't rethrow - call can continue without database persistence
    }
  }

  /// Update call state in database
  Future<void> _updateCallInDatabase(
    String callId,
    CallState state, {
    DateTime? answerTime,
    DateTime? endTime,
    int? durationSeconds,
    CallEndReason? endReason,
  }) async {
    debugPrint('💾 Updating call in database: $callId -> ${state.name}');

    try {
      final updateData = <String, dynamic>{
        'call_state': state.name,
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (answerTime != null) {
        updateData['answer_time'] = answerTime.toIso8601String();
      }
      if (endTime != null) {
        updateData['end_time'] = endTime.toIso8601String();
      }
      if (durationSeconds != null) {
        updateData['duration_seconds'] = durationSeconds;
      }
      if (endReason != null) {
        updateData['end_reason'] = endReason.name;
      }

      await _supabase.from('calls').update(updateData).eq('id', callId);

      debugPrint('✅ Call updated in database successfully');
    } catch (e) {
      debugPrint('❌ Error updating call in database: $e');
      // Don't rethrow - call can continue without database persistence
    }
  }

  /// Dispose the service
  void dispose() {
    debugPrint('📡 Disposing CallSignalingService');

    // Close all channels
    for (final channel in _channels.values) {
      channel.unsubscribe();
    }
    _channels.clear();

    // Close stream controllers
    _incomingCallController.close();
    _signalingDataController.close();
    _callEndedController.close();
    _callDeclinedController.close();
  }
}
