import 'package:flutter/material.dart';
import 'package:pulsemeet/services/realtime_debug_service.dart';
import 'package:pulsemeet/services/realtime_message_delivery_fix_service.dart';

/// Quick debug widget for testing real-time message delivery
class RealtimeDebugWidget extends StatefulWidget {
  final String conversationId;
  final String? pulseId;

  const RealtimeDebugWidget({
    Key? key,
    required this.conversationId,
    this.pulseId,
  }) : super(key: key);

  @override
  State<RealtimeDebugWidget> createState() => _RealtimeDebugWidgetState();
}

class _RealtimeDebugWidgetState extends State<RealtimeDebugWidget> {
  final RealtimeDebugService _debugService = RealtimeDebugService();
  final RealtimeMessageDeliveryFixService _fixService = RealtimeMessageDeliveryFixService();
  
  bool _isDebugging = false;
  bool _isTesting = false;
  bool _isFixing = false;
  String _debugReport = '';
  Map<String, dynamic> _realtimeStatus = {};

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Real-Time Debug Tools',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Conversation: ${widget.conversationId}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            if (widget.pulseId != null) ...[
              Text(
                'Pulse: ${widget.pulseId}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
            const SizedBox(height: 16),
            
            // Debug Controls
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isDebugging ? null : _startDebugging,
                  icon: Icon(_isDebugging ? Icons.bug_report : Icons.play_arrow),
                  label: Text(_isDebugging ? 'Debugging...' : 'Start Debug'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isDebugging ? Colors.orange : Colors.blue,
                  ),
                ),
                
                ElevatedButton.icon(
                  onPressed: _isTesting ? null : _runTest,
                  icon: Icon(_isTesting ? Icons.hourglass_empty : Icons.science),
                  label: Text(_isTesting ? 'Testing...' : 'Run Test'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                  ),
                ),
                
                if (widget.pulseId != null) ...[
                  ElevatedButton.icon(
                    onPressed: _isFixing ? null : _applyFix,
                    icon: Icon(_isFixing ? Icons.build : Icons.healing),
                    label: Text(_isFixing ? 'Fixing...' : 'Apply Fix'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                    ),
                  ),
                ],
                
                ElevatedButton.icon(
                  onPressed: _checkStatus,
                  icon: const Icon(Icons.info),
                  label: const Text('Check Status'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Status Display
            if (_realtimeStatus.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  border: Border.all(color: Colors.blue),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Real-Time Status:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text('Connected: ${_realtimeStatus['client_connected'] ?? 'Unknown'}'),
                    Text('Active Channels: ${_realtimeStatus['active_channels'] ?? 'Unknown'}'),
                    Text('Debug Channels: ${_realtimeStatus['debug_channels'] ?? 'Unknown'}'),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // Debug Report
            if (_debugReport.isNotEmpty) ...[
              ExpansionTile(
                title: const Text('Debug Report'),
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _debugReport,
                      style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _startDebugging() async {
    setState(() {
      _isDebugging = true;
    });

    try {
      await _debugService.startDebugging(widget.conversationId);
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Real-time debugging started'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to start debugging: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isDebugging = false;
      });
    }
  }

  Future<void> _runTest() async {
    setState(() {
      _isTesting = true;
    });

    try {
      final success = await _debugService.runComprehensiveTest(widget.conversationId);
      
      final report = _debugService.generateDebugReport(widget.conversationId);
      setState(() {
        _debugReport = report;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'Real-time test PASSED!' : 'Real-time test FAILED!'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Test failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isTesting = false;
      });
    }
  }

  Future<void> _applyFix() async {
    if (widget.pulseId == null) return;
    
    setState(() {
      _isFixing = true;
    });

    try {
      final success = await _fixService.fixRealtimeDeliveryForPulseGroup(widget.pulseId!);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'Real-time fix applied successfully!' : 'Real-time fix failed!'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Fix failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isFixing = false;
      });
    }
  }

  Future<void> _checkStatus() async {
    try {
      final status = await _debugService.checkRealtimeStatus();
      setState(() {
        _realtimeStatus = status;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Status updated'),
          backgroundColor: Colors.blue,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to check status: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    _debugService.dispose();
    super.dispose();
  }
}
