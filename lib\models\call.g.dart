// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'call.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SignalingData _$SignalingDataFromJson(Map<String, dynamic> json) =>
    SignalingData(
      type: json['type'] as String,
      data: json['data'] as Map<String, dynamic>,
      sdp: json['sdp'] as String?,
      candidate: json['candidate'] as String?,
      sdpMLineIndex: (json['sdpMLineIndex'] as num?)?.toInt(),
      sdpMid: json['sdpMid'] as String?,
    );

Map<String, dynamic> _$SignalingDataToJson(SignalingData instance) =>
    <String, dynamic>{
      'type': instance.type,
      'data': instance.data,
      'sdp': instance.sdp,
      'candidate': instance.candidate,
      'sdpMLineIndex': instance.sdpMLineIndex,
      'sdpMid': instance.sdpMid,
    };

CallParticipant _$CallParticipantFromJson(Map<String, dynamic> json) =>
    CallParticipant(
      userId: json['userId'] as String,
      name: json['name'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      isAudioMuted: json['isAudioMuted'] as bool? ?? false,
      isVideoMuted: json['isVideoMuted'] as bool? ?? false,
      joinedAt: DateTime.parse(json['joinedAt'] as String),
      leftAt: json['leftAt'] == null
          ? null
          : DateTime.parse(json['leftAt'] as String),
    );

Map<String, dynamic> _$CallParticipantToJson(CallParticipant instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'name': instance.name,
      'avatarUrl': instance.avatarUrl,
      'isAudioMuted': instance.isAudioMuted,
      'isVideoMuted': instance.isVideoMuted,
      'joinedAt': instance.joinedAt.toIso8601String(),
      'leftAt': instance.leftAt?.toIso8601String(),
    };

Call _$CallFromJson(Map<String, dynamic> json) => Call(
      id: json['id'] as String,
      conversationId: json['conversationId'] as String,
      type: $enumDecode(_$CallTypeEnumMap, json['type']),
      state: $enumDecode(_$CallStateEnumMap, json['state']),
      callerId: json['callerId'] as String,
      calleeId: json['calleeId'] as String,
      participants: (json['participants'] as List<dynamic>)
          .map((e) => CallParticipant.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      startedAt: json['startedAt'] == null
          ? null
          : DateTime.parse(json['startedAt'] as String),
      endedAt: json['endedAt'] == null
          ? null
          : DateTime.parse(json['endedAt'] as String),
      duration: (json['duration'] as num?)?.toInt(),
      endReason: $enumDecodeNullable(_$CallEndReasonEnumMap, json['endReason']),
      signalingData: json['signalingData'] == null
          ? null
          : SignalingData.fromJson(
              json['signalingData'] as Map<String, dynamic>),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$CallToJson(Call instance) => <String, dynamic>{
      'id': instance.id,
      'conversationId': instance.conversationId,
      'type': _$CallTypeEnumMap[instance.type]!,
      'state': _$CallStateEnumMap[instance.state]!,
      'callerId': instance.callerId,
      'calleeId': instance.calleeId,
      'participants': instance.participants,
      'createdAt': instance.createdAt.toIso8601String(),
      'startedAt': instance.startedAt?.toIso8601String(),
      'endedAt': instance.endedAt?.toIso8601String(),
      'duration': instance.duration,
      'endReason': _$CallEndReasonEnumMap[instance.endReason],
      'signalingData': instance.signalingData,
      'metadata': instance.metadata,
    };

const _$CallTypeEnumMap = {
  CallType.voice: 'voice',
  CallType.video: 'video',
};

const _$CallStateEnumMap = {
  CallState.idle: 'idle',
  CallState.outgoing: 'outgoing',
  CallState.incoming: 'incoming',
  CallState.ringing: 'ringing',
  CallState.connecting: 'connecting',
  CallState.connected: 'connected',
  CallState.ended: 'ended',
  CallState.declined: 'declined',
  CallState.missed: 'missed',
  CallState.failed: 'failed',
};

const _$CallEndReasonEnumMap = {
  CallEndReason.userEnded: 'user_ended',
  CallEndReason.remoteEnded: 'remote_ended',
  CallEndReason.declined: 'declined',
  CallEndReason.missed: 'missed',
  CallEndReason.networkError: 'network_error',
  CallEndReason.permissionDenied: 'permission_denied',
  CallEndReason.timeout: 'timeout',
};
