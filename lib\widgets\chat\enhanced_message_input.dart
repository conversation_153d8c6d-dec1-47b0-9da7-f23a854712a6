import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pulsemeet/models/conversation.dart';
import 'package:pulsemeet/services/voice_message_service.dart';

/// Enhanced message input widget with voice, location, and media support
class EnhancedMessageInput extends StatefulWidget {
  final Conversation conversation;
  final Function(String) onSendText;
  final Function(String, String?) onSendImage;
  final Function(String) onSendVoice;
  final Function() onSendLocation;
  final Function(bool) onTypingChanged;

  const EnhancedMessageInput({
    super.key,
    required this.conversation,
    required this.onSendText,
    required this.onSendImage,
    required this.onSendVoice,
    required this.onSendLocation,
    required this.onTypingChanged,
  });

  @override
  State<EnhancedMessageInput> createState() => _EnhancedMessageInputState();
}

class _EnhancedMessageInputState extends State<EnhancedMessageInput>
    with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isTyping = false;
  bool _isRecording = false;
  bool _showAttachmentMenu = false;

  late AnimationController _recordingAnimationController;
  late Animation<double> _recordingAnimation;

  @override
  void initState() {
    super.initState();
    _textController.addListener(_onTextChanged);

    // Initialize animations
    _recordingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _recordingAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _recordingAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    _recordingAnimationController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final isTyping = _textController.text.trim().isNotEmpty;
    if (isTyping != _isTyping) {
      setState(() {
        _isTyping = isTyping;
      });
      widget.onTypingChanged(isTyping);
    }
  }

  void _sendTextMessage() {
    final text = _textController.text.trim();
    if (text.isNotEmpty) {
      widget.onSendText(text);
      _textController.clear();
      setState(() {
        _isTyping = false;
      });
      widget.onTypingChanged(false);
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    setState(() {
      _showAttachmentMenu = false;
    });

    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: source,
      maxWidth: 1920,
      maxHeight: 1080,
      imageQuality: 85,
    );

    if (pickedFile != null) {
      widget.onSendImage(pickedFile.path, null);
    }
  }

  Future<void> _startVoiceRecording() async {
    debugPrint(
        '🎤 Starting voice recording for ${widget.conversation.isPulseGroup ? 'pulse group' : 'conversation'}');

    final permission = await Permission.microphone.request();
    if (permission.isGranted) {
      setState(() {
        _isRecording = true;
      });
      _recordingAnimationController.repeat(reverse: true);
      HapticFeedback.lightImpact();

      // Start actual recording using voice service
      final voiceService = VoiceMessageService();
      final success = await voiceService.startRecording();
      if (!success) {
        setState(() {
          _isRecording = false;
        });
        _recordingAnimationController.stop();
        _recordingAnimationController.reset();
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Microphone permission required for voice messages'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _stopVoiceRecording() async {
    if (_isRecording) {
      setState(() {
        _isRecording = false;
      });
      _recordingAnimationController.stop();
      _recordingAnimationController.reset();
      HapticFeedback.lightImpact();

      try {
        // Stop recording and get file path
        final voiceService = VoiceMessageService();
        final voiceFilePath = await voiceService.stopRecordingForPulseGroup();

        if (voiceFilePath != null) {
          debugPrint('✅ Voice recording completed: $voiceFilePath');
          widget.onSendVoice(voiceFilePath);
        } else {
          debugPrint('❌ Voice recording failed');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Failed to record voice message'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        debugPrint('❌ Error stopping voice recording: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error recording voice message: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1E1E1E) : Colors.white,
        border: Border(
          top: BorderSide(
            color: isDark ? Colors.grey[800]! : Colors.grey[300]!,
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Attachment menu
            if (_showAttachmentMenu) _buildAttachmentMenu(),

            // Main input row
            Row(
              children: [
                // Attachment button
                IconButton(
                  icon: Icon(
                    _showAttachmentMenu ? Icons.close : Icons.attach_file,
                    color: theme.colorScheme.primary,
                  ),
                  onPressed: () {
                    setState(() {
                      _showAttachmentMenu = !_showAttachmentMenu;
                    });
                  },
                ),

                // Text input
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color:
                          isDark ? const Color(0xFF2A2A2A) : Colors.grey[100],
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: TextField(
                      controller: _textController,
                      focusNode: _focusNode,
                      decoration: InputDecoration(
                        hintText: 'Type a message...',
                        hintStyle: TextStyle(
                          color: isDark ? Colors.grey[400] : Colors.grey[600],
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      style: TextStyle(
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                      maxLines: 4,
                      minLines: 1,
                      textCapitalization: TextCapitalization.sentences,
                      onSubmitted: (_) => _sendTextMessage(),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Voice/Send button
                if (_isTyping)
                  IconButton(
                    icon: Icon(
                      Icons.send,
                      color: theme.colorScheme.primary,
                    ),
                    onPressed: _sendTextMessage,
                  )
                else
                  GestureDetector(
                    onLongPressStart: (_) => _startVoiceRecording(),
                    onLongPressEnd: (_) => _stopVoiceRecording(),
                    child: AnimatedBuilder(
                      animation: _recordingAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _isRecording ? _recordingAnimation.value : 1.0,
                          child: Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              color: _isRecording
                                  ? Colors.red
                                  : theme.colorScheme.primary,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              _isRecording ? Icons.stop : Icons.mic,
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
              ],
            ),

            // Recording indicator
            if (_isRecording)
              Container(
                margin: const EdgeInsets.only(top: 8),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Recording voice message...',
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentMenu() {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildAttachmentOption(
            icon: Icons.camera_alt,
            label: 'Camera',
            color: Colors.blue,
            onTap: () => _pickImage(ImageSource.camera),
          ),
          _buildAttachmentOption(
            icon: Icons.photo_library,
            label: 'Gallery',
            color: Colors.green,
            onTap: () => _pickImage(ImageSource.gallery),
          ),
          _buildAttachmentOption(
            icon: Icons.location_on,
            label: 'Location',
            color: Colors.red,
            onTap: () {
              setState(() {
                _showAttachmentMenu = false;
              });
              widget.onSendLocation();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle,
              border: Border.all(color: color.withOpacity(0.3)),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
