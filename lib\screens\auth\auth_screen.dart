import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pulsemeet/services/supabase_service.dart';
import 'package:pulsemeet/screens/auth/phone_auth_screen.dart';

/// Authentication screen with options to sign in
class AuthScreen extends StatelessWidget {
  const AuthScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final supabaseService =
        Provider.of<SupabaseService>(context, listen: false);

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Spacer(),
              // App logo
              Center(
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.people_alt_rounded,
                      size: 80,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 24),
              // App name
              const Center(
                child: Text(
                  'PulseMeet',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // App description
              const Center(
                child: Text(
                  'Connect with people nearby for spontaneous meetups',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ),
              const Spacer(),
              // Phone sign in button
              ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PhoneAuthScreen(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Continue with Phone'),
              ),
              const SizedBox(height: 16),
              // Google sign in button
              OutlinedButton.icon(
                onPressed: () async {
                  try {
                    // Show loading indicator
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Opening Google Sign-In...'),
                        duration: Duration(seconds: 2),
                      ),
                    );

                    await supabaseService.signInWithGoogle();

                    // Note: The actual sign-in completion will be handled by the auth state listener
                    // in the app's main widget, which will navigate to the home screen when signed in
                  } catch (e) {
                    if (context.mounted) {
                      // Provide user-friendly error messages
                      String errorMessage = 'Sign-in failed. Please try again.';

                      if (e.toString().contains('cancelled')) {
                        errorMessage = 'Sign-in was cancelled.';
                      } else if (e.toString().contains('ApiException: 10') ||
                          e.toString().contains('DEVELOPER_ERROR')) {
                        errorMessage =
                            'Google Sign-In configuration error. Please contact support.';
                      } else if (e.toString().contains('network') ||
                          e.toString().contains('connection')) {
                        errorMessage =
                            'Network error. Please check your connection and try again.';
                      }

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(errorMessage),
                          backgroundColor: Colors.red,
                          duration: const Duration(seconds: 4),
                        ),
                      );
                    }
                  }
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                icon: const Icon(Icons.g_mobiledata, size: 24),
                label: const Text('Continue with Google'),
              ),
              const SizedBox(height: 16),
              // Apple sign in button (disabled)
              Tooltip(
                message: 'Apple Sign-In is currently unavailable on Android',
                child: OutlinedButton.icon(
                  onPressed: null, // Disabled
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  icon: const Icon(Icons.apple, size: 24),
                  label: const Text('Continue with Apple (Unavailable)'),
                ),
              ),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }
}
