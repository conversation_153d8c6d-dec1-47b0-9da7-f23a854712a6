import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:pulsemeet/models/message.dart';
import 'package:pulsemeet/services/voice_message_service.dart';

/// Widget for displaying and playing voice messages
class VoiceMessageWidget extends StatefulWidget {
  final Message message;
  final bool isCurrentUser;

  const VoiceMessageWidget({
    super.key,
    required this.message,
    required this.isCurrentUser,
  });

  @override
  State<VoiceMessageWidget> createState() => _VoiceMessageWidgetState();
}

class _VoiceMessageWidgetState extends State<VoiceMessageWidget>
    with TickerProviderStateMixin {
  final VoiceMessageService _voiceService = VoiceMessageService();

  bool _isPlaying = false;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  List<double> _waveform = [];

  late AnimationController _playButtonController;
  late AnimationController _waveformController;
  late Animation<double> _playButtonAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadVoiceMessageData();
    _setupPlaybackListener();
  }

  void _initializeAnimations() {
    _playButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _waveformController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _playButtonAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _playButtonController,
      curve: Curves.easeInOut,
    ));
  }

  void _loadVoiceMessageData() {
    if (widget.message.mediaData != null) {
      final mediaData = widget.message.mediaData!;

      // Extract voice message data from MediaData
      if (mediaData.duration != null) {
        _totalDuration = Duration(seconds: mediaData.duration!);
      }

      // Generate default waveform since MediaData doesn't store waveform
      _waveform = List.generate(50, (index) => 0.3 + (index % 3) * 0.2);

      setState(() {});
    }
  }

  void _setupPlaybackListener() {
    _voiceService.playbackStateStream.listen((state) {
      if (mounted) {
        setState(() {
          _isPlaying = state.isPlaying;
          _currentPosition = state.position;
          if (state.duration > Duration.zero) {
            _totalDuration = state.duration;
          }
        });
      }
    });
  }

  Future<void> _togglePlayback() async {
    if (_isPlaying) {
      await _voiceService.stopPlayback();
      _playButtonController.reverse();
    } else {
      if (widget.message.mediaData != null) {
        // Create voice message data from the message
        // For now, use a placeholder since MediaData.url is a URL, not raw data
        final voiceData = VoiceMessageData(
          encryptedData:
              Uint8List(0), // Placeholder - would need to download from URL
          duration: _totalDuration,
          waveform: _waveform,
        );

        final success = await _voiceService.playVoiceMessage(voiceData);
        if (success) {
          _playButtonController.forward();
          _waveformController.forward();
        }
      }
    }
  }

  @override
  void dispose() {
    _playButtonController.dispose();
    _waveformController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      constraints: const BoxConstraints(maxWidth: 280),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: widget.isCurrentUser
            ? theme.colorScheme.primary
            : (isDark ? const Color(0xFF2A2A2A) : Colors.grey[100]),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Voice message controls
          Row(
            children: [
              // Play/pause button
              AnimatedBuilder(
                animation: _playButtonAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _playButtonAnimation.value,
                    child: GestureDetector(
                      onTap: _togglePlayback,
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: widget.isCurrentUser
                              ? Colors.white.withOpacity(0.2)
                              : theme.colorScheme.primary.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          _isPlaying ? Icons.pause : Icons.play_arrow,
                          color: widget.isCurrentUser
                              ? Colors.white
                              : theme.colorScheme.primary,
                          size: 20,
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(width: 12),

              // Waveform and duration
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Waveform visualization
                    SizedBox(
                      height: 32,
                      child: _buildWaveform(),
                    ),

                    const SizedBox(height: 4),

                    // Duration and progress
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _formatDuration(_currentPosition),
                          style: TextStyle(
                            fontSize: 12,
                            color: widget.isCurrentUser
                                ? Colors.white.withOpacity(0.8)
                                : (isDark
                                    ? Colors.grey[400]
                                    : Colors.grey[600]),
                          ),
                        ),
                        Text(
                          _formatDuration(_totalDuration),
                          style: TextStyle(
                            fontSize: 12,
                            color: widget.isCurrentUser
                                ? Colors.white.withOpacity(0.8)
                                : (isDark
                                    ? Colors.grey[400]
                                    : Colors.grey[600]),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Voice message indicator
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.mic,
                size: 14,
                color: widget.isCurrentUser
                    ? Colors.white.withOpacity(0.7)
                    : (isDark ? Colors.grey[500] : Colors.grey[500]),
              ),
              const SizedBox(width: 4),
              Text(
                'Voice message',
                style: TextStyle(
                  fontSize: 12,
                  color: widget.isCurrentUser
                      ? Colors.white.withOpacity(0.7)
                      : (isDark ? Colors.grey[500] : Colors.grey[500]),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWaveform() {
    if (_waveform.isEmpty) {
      return Container(
        height: 32,
        decoration: BoxDecoration(
          color: widget.isCurrentUser
              ? Colors.white.withOpacity(0.1)
              : Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Center(
          child: Text(
            'Loading...',
            style: TextStyle(fontSize: 10),
          ),
        ),
      );
    }

    final progress = _totalDuration.inMilliseconds > 0
        ? _currentPosition.inMilliseconds / _totalDuration.inMilliseconds
        : 0.0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: _waveform.asMap().entries.map((entry) {
        final index = entry.key;
        final amplitude = entry.value;
        final isActive = index / _waveform.length <= progress;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 100),
          width: 2,
          height: 4 + (amplitude * 24),
          decoration: BoxDecoration(
            color: isActive
                ? (widget.isCurrentUser
                    ? Colors.white
                    : Theme.of(context).colorScheme.primary)
                : (widget.isCurrentUser
                    ? Colors.white.withOpacity(0.3)
                    : Colors.grey.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(1),
          ),
        );
      }).toList(),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(1, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
