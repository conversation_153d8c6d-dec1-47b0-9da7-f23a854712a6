import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:permission_handler/permission_handler.dart';

import 'package:pulsemeet/models/call.dart';
import 'package:pulsemeet/services/call_service.dart';
import 'package:pulsemeet/services/call_signaling_service.dart';
import 'package:pulsemeet/services/conversation_service.dart';

/// High-level service that manages calls and coordinates between call service and signaling
class CallManagerService {
  static final CallManagerService _instance = CallManagerService._internal();
  factory CallManagerService() => _instance;
  CallManagerService._internal();

  final CallService _callService = CallService();
  final CallSignalingService _signalingService = CallSignalingService();
  final ConversationService _conversationService = ConversationService();

  // Stream subscriptions
  StreamSubscription<Call>? _incomingCallSubscription;
  StreamSubscription<SignalingData>? _signalingDataSubscription;
  StreamSubscription<String>? _callEndedSubscription;
  StreamSubscription<String>? _callDeclinedSubscription;

  // Current call state
  Call? _currentCall;
  bool _isInitialized = false;

  // Stream controllers
  final StreamController<Call?> _callStateController =
      StreamController<Call?>.broadcast();
  final StreamController<MediaStream?> _localStreamController =
      StreamController<MediaStream?>.broadcast();
  final StreamController<MediaStream?> _remoteStreamController =
      StreamController<MediaStream?>.broadcast();

  // Getters
  Stream<Call?> get callStateStream => _callStateController.stream;
  Stream<MediaStream?> get localStreamStream => _localStreamController.stream;
  Stream<MediaStream?> get remoteStreamStream => _remoteStreamController.stream;
  Call? get currentCall => _currentCall;
  bool get hasActiveCall => _currentCall != null && !_currentCall!.isEnded;

  /// Initialize the call manager
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('⚠️ CallManagerService already initialized');
      return;
    }

    debugPrint('📞 Initializing CallManagerService...');

    try {
      // Initialize services
      await _callService.initialize();
      await _signalingService.initialize();

      // Inject signaling service into call service
      _callService.setSignalingService(_signalingService);

      // Setup stream subscriptions
      _setupStreamSubscriptions();

      _isInitialized = true;
      debugPrint('✅ CallManagerService initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing CallManagerService: $e');
      rethrow;
    }
  }

  /// Setup stream subscriptions
  void _setupStreamSubscriptions() {
    debugPrint('📞 Setting up call manager stream subscriptions');

    // Listen for incoming calls
    _incomingCallSubscription = _signalingService.incomingCallStream.listen(
      (call) {
        debugPrint('📞 Incoming call received: ${call.id}');
        _handleIncomingCall(call);
      },
      onError: (error) {
        debugPrint('❌ Error in incoming call stream: $error');
      },
    );

    // Listen for signaling data
    _signalingDataSubscription = _signalingService.signalingDataStream.listen(
      (data) {
        debugPrint('📡 Signaling data received: ${data.type}');
        _handleSignalingData(data);
      },
      onError: (error) {
        debugPrint('❌ Error in signaling data stream: $error');
      },
    );

    // Listen for call ended events
    _callEndedSubscription = _signalingService.callEndedStream.listen(
      (callId) {
        debugPrint('📞 Call ended remotely: $callId');
        _handleRemoteCallEnd(callId);
      },
      onError: (error) {
        debugPrint('❌ Error in call ended stream: $error');
      },
    );

    // Listen for call declined events
    _callDeclinedSubscription = _signalingService.callDeclinedStream.listen(
      (callId) {
        debugPrint('📞 Call declined remotely: $callId');
        _handleRemoteCallDecline(callId);
      },
      onError: (error) {
        debugPrint('❌ Error in call declined stream: $error');
      },
    );

    // Listen for call service state changes
    _callService.callStateStream.listen(
      (call) {
        _currentCall = call;
        _callStateController.add(call);
      },
      onError: (error) {
        debugPrint('❌ Error in call service state stream: $error');
      },
    );

    // Listen for media streams
    _callService.localStreamStream.listen(
      (stream) => _localStreamController.add(stream),
      onError: (error) {
        debugPrint('❌ Error in local stream: $error');
      },
    );

    _callService.remoteStreamStream.listen(
      (stream) => _remoteStreamController.add(stream),
      onError: (error) {
        debugPrint('❌ Error in remote stream: $error');
      },
    );
  }

  /// Start a call
  Future<void> startCall({
    required String conversationId,
    required String calleeId,
    required CallType type,
  }) async {
    debugPrint('📞 Starting ${type.name} call to $calleeId');

    try {
      // Check permissions
      await _checkPermissions(type);

      // Check if there's already an active call
      if (hasActiveCall) {
        throw Exception('Another call is already in progress');
      }

      // Get callee information
      final calleeInfo = await _getCalleeInfo(calleeId);

      // Start the call through call service
      await _callService.startCall(
        conversationId: conversationId,
        calleeId: calleeId,
        type: type,
      );

      // CRITICAL FIX: Get the current call immediately after creation
      _currentCall = _callService.currentCall;

      // Emit the call state immediately to ensure stream listeners get it
      if (_currentCall != null) {
        _callStateController.add(_currentCall);

        // Setup signaling for this call
        await _signalingService.setupCallSignaling(_currentCall!.id);

        // Send call invitation
        await _signalingService.sendCallInvitation(
          call: _currentCall!,
          calleeName: calleeInfo['name'] ?? 'Unknown',
          calleeAvatarUrl: calleeInfo['avatar_url'],
        );
      } else {
        throw Exception('Failed to create call');
      }

      debugPrint('✅ Call started successfully');
    } catch (e) {
      debugPrint('❌ Error starting call: $e');
      rethrow;
    }
  }

  /// Answer an incoming call
  Future<void> answerCall(String callId) async {
    debugPrint('📞 Answering call: $callId');

    try {
      // Check permissions
      if (_currentCall != null) {
        await _checkPermissions(_currentCall!.type);
      }

      // Setup signaling for this call
      await _signalingService.setupCallSignaling(callId);

      // Answer through call service
      await _callService.answerCall(callId);

      // Send answer signal
      await _signalingService.sendCallAnswer(callId);

      debugPrint('✅ Call answered successfully');
    } catch (e) {
      debugPrint('❌ Error answering call: $e');
      rethrow;
    }
  }

  /// Decline an incoming call
  Future<void> declineCall(String callId) async {
    debugPrint('📞 Declining call: $callId');

    try {
      // Send decline signal
      await _signalingService.sendCallDecline(callId);

      // Decline through call service
      await _callService.declineCall(callId);

      // Close signaling
      await _signalingService.closeCallSignaling(callId);

      debugPrint('✅ Call declined successfully');
    } catch (e) {
      debugPrint('❌ Error declining call: $e');
    }
  }

  /// End the current call
  Future<void> endCall() async {
    debugPrint('📞 Ending current call');

    try {
      if (_currentCall != null) {
        // Send end signal
        await _signalingService.sendCallEnd(
          _currentCall!.id,
          CallEndReason.userEnded,
        );

        // End through call service
        await _callService.endCall();

        // Close signaling
        await _signalingService.closeCallSignaling(_currentCall!.id);
      }

      debugPrint('✅ Call ended successfully');
    } catch (e) {
      debugPrint('❌ Error ending call: $e');
    }
  }

  /// Toggle microphone
  Future<void> toggleMicrophone() async {
    await _callService.toggleMicrophone();
  }

  /// Toggle speaker
  Future<void> toggleSpeaker() async {
    await _callService.toggleSpeaker();
  }

  /// Toggle camera (for video calls)
  Future<void> toggleCamera() async {
    await _callService.toggleCamera();
  }

  /// Switch camera (front/back)
  Future<void> switchCamera() async {
    await _callService.switchCamera();
  }

  /// Handle incoming call
  void _handleIncomingCall(Call call) {
    debugPrint('📞 Handling incoming call: ${call.id}');

    // Check if there's already an active call
    if (hasActiveCall) {
      debugPrint('⚠️ Rejecting incoming call due to active call');
      declineCall(call.id);
      return;
    }

    _currentCall = call;
    _callStateController.add(call);
  }

  /// Handle signaling data
  void _handleSignalingData(SignalingData data) {
    debugPrint('📡 Handling signaling data: ${data.type}');

    // Forward to call service for WebRTC processing
    _callService.processSignalingData(data);
  }

  /// Handle remote call end
  void _handleRemoteCallEnd(String callId) {
    debugPrint('📞 Handling remote call end: $callId');

    if (_currentCall?.id == callId) {
      _callService.endCall();
      _signalingService.closeCallSignaling(callId);
    }
  }

  /// Handle remote call decline
  void _handleRemoteCallDecline(String callId) {
    debugPrint('📞 Handling remote call decline: $callId');

    if (_currentCall?.id == callId) {
      _currentCall = _currentCall!.copyWith(
        state: CallState.declined,
        endedAt: DateTime.now(),
        endReason: CallEndReason.declined,
      );
      _callStateController.add(_currentCall);
      _signalingService.closeCallSignaling(callId);
    }
  }

  /// Check required permissions
  Future<void> _checkPermissions(CallType type) async {
    debugPrint('🔐 Checking permissions for ${type.name} call');

    // Check microphone permission
    final micPermission = await Permission.microphone.request();
    if (!micPermission.isGranted) {
      throw Exception('Microphone permission is required for calls');
    }

    // Check camera permission for video calls
    if (type == CallType.video) {
      final cameraPermission = await Permission.camera.request();
      if (!cameraPermission.isGranted) {
        throw Exception('Camera permission is required for video calls');
      }
    }

    debugPrint('✅ Permissions granted');
  }

  /// Get callee information
  Future<Map<String, dynamic>> _getCalleeInfo(String calleeId) async {
    try {
      // Get user profile from conversation service or user service
      // For now, return basic info
      return {
        'name': 'User',
        'avatar_url': null,
      };
    } catch (e) {
      debugPrint('⚠️ Error getting callee info: $e');
      return {
        'name': 'Unknown',
        'avatar_url': null,
      };
    }
  }

  /// Dispose the service
  void dispose() {
    debugPrint('📞 Disposing CallManagerService');

    // Cancel subscriptions
    _incomingCallSubscription?.cancel();
    _signalingDataSubscription?.cancel();
    _callEndedSubscription?.cancel();
    _callDeclinedSubscription?.cancel();

    // Close stream controllers
    _callStateController.close();
    _localStreamController.close();
    _remoteStreamController.close();

    // Dispose services
    _callService.dispose();
    _signalingService.dispose();

    _isInitialized = false;
  }
}
