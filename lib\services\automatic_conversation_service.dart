import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:pulsemeet/models/conversation.dart';
import 'package:pulsemeet/models/pulse.dart';

/// Service for automatically managing pulse group conversations
/// Handles automatic creation and participant management
class AutomaticConversationService {
  static final AutomaticConversationService _instance = AutomaticConversationService._internal();
  factory AutomaticConversationService() => _instance;
  AutomaticConversationService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  
  /// Automatically create conversation for a pulse (called after pulse creation)
  Future<Conversation?> createPulseConversationAutomatic(String pulseId) async {
    try {
      debugPrint('🤖 AutoConversationService: Creating automatic conversation for pulse: $pulseId');
      
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        debugPrint('❌ AutoConversationService: No authenticated user');
        return null;
      }

      // Use the database function to create conversation
      final response = await _supabase.rpc('create_pulse_conversation', params: {
        'pulse_id_param': pulseId,
      });

      if (response == null) {
        throw Exception('RPC returned null response');
      }

      String conversationId;
      if (response is String) {
        conversationId = response;
      } else if (response is Map && response.containsKey('id')) {
        conversationId = response['id'] as String;
      } else {
        throw Exception('Invalid RPC response format: $response');
      }

      debugPrint('✅ AutoConversationService: Conversation created with ID: $conversationId');

      // Fetch the created conversation
      final conversationData = await _supabase
          .from('conversations')
          .select()
          .eq('id', conversationId)
          .single();

      final conversation = Conversation.fromJson(conversationData);
      debugPrint('✅ AutoConversationService: Successfully created automatic conversation');
      
      return conversation;
    } catch (e) {
      debugPrint('❌ AutoConversationService: Error creating automatic conversation: $e');
      return null;
    }
  }

  /// Automatically add user to pulse conversation (called after joining pulse)
  Future<bool> addUserToPulseConversationAutomatic(String pulseId, String userId) async {
    try {
      debugPrint('🤖 AutoConversationService: Adding user $userId to pulse $pulseId conversation');

      // Use the database function to add user to conversation
      final response = await _supabase.rpc('add_user_to_pulse_conversation', params: {
        'pulse_id_param': pulseId,
        'user_id_param': userId,
      });

      if (response == true) {
        debugPrint('✅ AutoConversationService: Successfully added user to conversation');
        return true;
      } else {
        debugPrint('⚠️ AutoConversationService: RPC returned false');
        return false;
      }
    } catch (e) {
      debugPrint('❌ AutoConversationService: Error adding user to conversation: $e');
      return false;
    }
  }

  /// Get existing conversation for a pulse (used for navigation)
  Future<Conversation?> getPulseConversation(String pulseId) async {
    try {
      debugPrint('🔍 AutoConversationService: Getting conversation for pulse: $pulseId');

      final response = await _supabase
          .from('conversations')
          .select()
          .eq('pulse_id', pulseId)
          .eq('type', 'pulse_group')
          .maybeSingle();

      if (response != null) {
        final conversation = Conversation.fromJson(response);
        debugPrint('✅ AutoConversationService: Found existing conversation: ${conversation.id}');
        return conversation;
      } else {
        debugPrint('⚠️ AutoConversationService: No conversation found for pulse');
        return null;
      }
    } catch (e) {
      debugPrint('❌ AutoConversationService: Error getting pulse conversation: $e');
      return null;
    }
  }

  /// Ensure user is participant in pulse conversation
  Future<bool> ensureUserIsParticipant(String pulseId, String userId) async {
    try {
      debugPrint('🔍 AutoConversationService: Ensuring user $userId is participant in pulse $pulseId');

      // First get the conversation
      final conversation = await getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('⚠️ AutoConversationService: No conversation found, creating one');
        final newConversation = await createPulseConversationAutomatic(pulseId);
        return newConversation != null;
      }

      // Check if user is already a participant
      final existingParticipant = await _supabase
          .from('conversation_participants')
          .select('id')
          .eq('conversation_id', conversation.id)
          .eq('user_id', userId)
          .maybeSingle();

      if (existingParticipant != null) {
        debugPrint('✅ AutoConversationService: User is already a participant');
        return true;
      }

      // Add user as participant
      await _supabase.from('conversation_participants').insert({
        'conversation_id': conversation.id,
        'user_id': userId,
        'role': 'member',
        'joined_at': DateTime.now().toIso8601String(),
      });

      debugPrint('✅ AutoConversationService: Added user as participant');
      return true;
    } catch (e) {
      debugPrint('❌ AutoConversationService: Error ensuring user participation: $e');
      return false;
    }
  }

  /// Get all pulse conversations for a user (used by ChatListScreen)
  Future<List<Conversation>> getUserPulseConversations() async {
    try {
      debugPrint('🔍 AutoConversationService: Getting user pulse conversations');

      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        debugPrint('❌ AutoConversationService: No authenticated user');
        return [];
      }

      final response = await _supabase
          .from('conversations')
          .select()
          .eq('type', 'pulse_group')
          .order('last_message_at', ascending: false);

      final conversations = response
          .map((data) => Conversation.fromJson(data))
          .toList();

      debugPrint('✅ AutoConversationService: Found ${conversations.length} pulse conversations');
      return conversations;
    } catch (e) {
      debugPrint('❌ AutoConversationService: Error getting user conversations: $e');
      return [];
    }
  }

  /// Check if user has access to pulse conversation
  Future<bool> canUserAccessConversation(String pulseId, String userId) async {
    try {
      // Check if user is a pulse participant
      final participant = await _supabase
          .from('pulse_participants')
          .select('status')
          .eq('pulse_id', pulseId)
          .eq('user_id', userId)
          .maybeSingle();

      if (participant != null && participant['status'] == 'active') {
        return true;
      }

      // Check if user is pulse creator
      final pulse = await _supabase
          .from('pulses')
          .select('creator_id')
          .eq('id', pulseId)
          .single();

      return pulse['creator_id'] == userId;
    } catch (e) {
      debugPrint('❌ AutoConversationService: Error checking user access: $e');
      return false;
    }
  }

  /// Initialize automatic conversation system
  Future<void> initializeAutomaticConversations() async {
    try {
      debugPrint('🚀 AutoConversationService: Initializing automatic conversation system');

      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        debugPrint('❌ AutoConversationService: No authenticated user for initialization');
        return;
      }

      // Get all pulses user participates in
      final userPulses = await _supabase
          .from('pulse_participants')
          .select('pulse_id')
          .eq('user_id', userId)
          .eq('status', 'active');

      debugPrint('🔍 AutoConversationService: Found ${userPulses.length} active pulse participations');

      // Ensure conversations exist for all user's pulses
      for (final pulseData in userPulses) {
        final pulseId = pulseData['pulse_id'] as String;
        await ensureUserIsParticipant(pulseId, userId);
      }

      debugPrint('✅ AutoConversationService: Initialization completed');
    } catch (e) {
      debugPrint('❌ AutoConversationService: Error during initialization: $e');
    }
  }
}
