import 'package:flutter/foundation.dart';
import 'package:pulsemeet/services/pulse_group_message_sync_fix_service.dart';
import 'package:pulsemeet/services/pulse_group_realtime_diagnostic_service.dart';
import 'package:pulsemeet/services/enhanced_realtime_sync_service.dart';
import 'package:pulsemeet/services/conversation_service.dart';
import 'package:pulsemeet/services/automatic_conversation_service.dart';

/// Integration test for the complete pulse group messaging solution
class PulseGroupMessagingIntegrationTest {
  static final PulseGroupMessagingIntegrationTest _instance = PulseGroupMessagingIntegrationTest._internal();
  factory PulseGroupMessagingIntegrationTest() => _instance;
  PulseGroupMessagingIntegrationTest._internal();

  final PulseGroupMessageSyncFixService _fixService = PulseGroupMessageSyncFixService();
  final PulseGroupRealtimeDiagnosticService _diagnosticService = PulseGroupRealtimeDiagnosticService();
  final EnhancedRealtimeSyncService _enhancedSync = EnhancedRealtimeSyncService();
  final ConversationService _conversationService = ConversationService();
  final AutomaticConversationService _autoConversationService = AutomaticConversationService();

  /// Run complete integration test for pulse group messaging
  Future<IntegrationTestResults> runCompleteIntegrationTest(String pulseId) async {
    final results = IntegrationTestResults();
    
    try {
      debugPrint('🧪 Starting complete pulse group messaging integration test for pulse: $pulseId');

      // Phase 1: Initial Diagnostic
      debugPrint('📋 Phase 1: Running initial diagnostic...');
      results.initialDiagnostic = await _diagnosticService.runComprehensiveDiagnostic(pulseId);
      debugPrint('📊 Initial diagnostic success rate: ${results.initialDiagnostic.getSuccessRate()}%');

      // Phase 2: Apply Fixes
      debugPrint('🔧 Phase 2: Applying automatic fixes...');
      results.fixResults = await _fixService.diagnoseAndFixPulseGroupSync(pulseId);
      debugPrint('📊 Fix success rate: ${results.fixResults.getSuccessRate()}%');

      // Phase 3: Post-Fix Diagnostic
      debugPrint('📋 Phase 3: Running post-fix diagnostic...');
      results.postFixDiagnostic = await _diagnosticService.runComprehensiveDiagnostic(pulseId);
      debugPrint('📊 Post-fix diagnostic success rate: ${results.postFixDiagnostic.getSuccessRate()}%');

      // Phase 4: Real Message Test
      debugPrint('💬 Phase 4: Testing real message delivery...');
      results.messageDeliveryTest = await _testRealMessageDelivery(pulseId);

      // Phase 5: Enhanced Sync Test
      debugPrint('⚡ Phase 5: Testing enhanced real-time sync...');
      results.enhancedSyncTest = await _testEnhancedRealtimeSync(pulseId);

      // Phase 6: Multi-User Simulation
      debugPrint('👥 Phase 6: Simulating multi-user messaging...');
      results.multiUserTest = await _testMultiUserMessaging(pulseId);

      // Calculate overall success
      results.calculateOverallSuccess();

      debugPrint('🧪 Complete integration test finished');
      debugPrint('📊 Overall success rate: ${results.overallSuccessRate}%');
      
      return results;
    } catch (e) {
      debugPrint('❌ Error during integration test: $e');
      return results;
    }
  }

  /// Test real message delivery
  Future<bool> _testRealMessageDelivery(String pulseId) async {
    try {
      debugPrint('💬 Testing real message delivery...');
      
      final conversation = await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for message delivery test');
        return false;
      }

      // Send multiple test messages
      final testMessages = [
        'Integration test message 1 - ${DateTime.now().millisecondsSinceEpoch}',
        'Integration test message 2 - ${DateTime.now().millisecondsSinceEpoch}',
        'Integration test message 3 - ${DateTime.now().millisecondsSinceEpoch}',
      ];

      int successCount = 0;
      for (final messageContent in testMessages) {
        final sentMessage = await _conversationService.sendTextMessage(conversation.id, messageContent);
        if (sentMessage != null) {
          successCount++;
          debugPrint('✅ Message sent successfully: ${sentMessage.id}');
          
          // Wait a bit between messages
          await Future.delayed(const Duration(milliseconds: 500));
        } else {
          debugPrint('❌ Failed to send message: $messageContent');
        }
      }

      final successRate = (successCount / testMessages.length) * 100;
      debugPrint('📊 Message delivery test: $successCount/${testMessages.length} messages sent (${successRate.toStringAsFixed(1)}%)');
      
      return successCount == testMessages.length;
    } catch (e) {
      debugPrint('❌ Error testing real message delivery: $e');
      return false;
    }
  }

  /// Test enhanced real-time sync
  Future<bool> _testEnhancedRealtimeSync(String pulseId) async {
    try {
      debugPrint('⚡ Testing enhanced real-time sync...');
      
      final conversation = await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for enhanced sync test');
        return false;
      }

      // Initialize enhanced sync
      await _enhancedSync.initializeConversationSync(conversation.id, pulseId);
      debugPrint('✅ Enhanced sync initialized');

      // Test message stream
      final messageStream = _enhancedSync.getMessageStream(conversation.id);
      if (messageStream != null) {
        debugPrint('✅ Message stream available');
        
        // Send a test message through enhanced sync
        final testMessage = 'Enhanced sync test - ${DateTime.now().millisecondsSinceEpoch}';
        final sentMessage = await _conversationService.sendTextMessage(conversation.id, testMessage);
        
        if (sentMessage != null) {
          debugPrint('✅ Enhanced sync test message sent');
          return true;
        } else {
          debugPrint('❌ Enhanced sync test message failed');
          return false;
        }
      } else {
        debugPrint('❌ Message stream not available');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error testing enhanced real-time sync: $e');
      return false;
    }
  }

  /// Test multi-user messaging simulation
  Future<bool> _testMultiUserMessaging(String pulseId) async {
    try {
      debugPrint('👥 Testing multi-user messaging simulation...');
      
      final conversation = await _autoConversationService.getPulseConversation(pulseId);
      if (conversation == null) {
        debugPrint('❌ No conversation found for multi-user test');
        return false;
      }

      // Simulate rapid message sending (like multiple users typing)
      final rapidMessages = [
        'Multi-user test 1',
        'Multi-user test 2',
        'Multi-user test 3',
        'Multi-user test 4',
        'Multi-user test 5',
      ];

      final futures = rapidMessages.map((content) => 
        _conversationService.sendTextMessage(conversation.id, '$content - ${DateTime.now().millisecondsSinceEpoch}')
      ).toList();

      // Send all messages concurrently
      final results = await Future.wait(futures);
      final successCount = results.where((msg) => msg != null).length;
      
      final successRate = (successCount / rapidMessages.length) * 100;
      debugPrint('📊 Multi-user test: $successCount/${rapidMessages.length} messages sent (${successRate.toStringAsFixed(1)}%)');
      
      return successCount >= (rapidMessages.length * 0.8); // 80% success rate acceptable
    } catch (e) {
      debugPrint('❌ Error testing multi-user messaging: $e');
      return false;
    }
  }

  /// Generate comprehensive integration test report
  String generateIntegrationTestReport(IntegrationTestResults results) {
    final buffer = StringBuffer();
    buffer.writeln('🧪 PULSE GROUP MESSAGING INTEGRATION TEST REPORT');
    buffer.writeln('================================================');
    buffer.writeln('');
    buffer.writeln('📊 Overall Success Rate: ${results.overallSuccessRate}%');
    buffer.writeln('');
    
    buffer.writeln('📋 Test Phase Results:');
    buffer.writeln('• Initial Diagnostic: ${results.initialDiagnostic.getSuccessRate()}% (${results.initialDiagnostic.getSuccessCount()}/6 tests passed)');
    buffer.writeln('• Fix Application: ${results.fixResults.getSuccessRate()}% (${results.fixResults.getSuccessCount()}/6 fixes applied)');
    buffer.writeln('• Post-Fix Diagnostic: ${results.postFixDiagnostic.getSuccessRate()}% (${results.postFixDiagnostic.getSuccessCount()}/6 tests passed)');
    buffer.writeln('• Message Delivery Test: ${results.messageDeliveryTest ? '✅ PASS' : '❌ FAIL'}');
    buffer.writeln('• Enhanced Sync Test: ${results.enhancedSyncTest ? '✅ PASS' : '❌ FAIL'}');
    buffer.writeln('• Multi-User Test: ${results.multiUserTest ? '✅ PASS' : '❌ FAIL'}');
    buffer.writeln('');
    
    buffer.writeln('📈 Improvement Analysis:');
    final improvement = results.postFixDiagnostic.getSuccessRate() - results.initialDiagnostic.getSuccessRate();
    if (improvement > 0) {
      buffer.writeln('✅ System improved by ${improvement.toStringAsFixed(1)}% after fixes');
    } else if (improvement == 0) {
      buffer.writeln('➡️ System performance maintained (no degradation)');
    } else {
      buffer.writeln('⚠️ System performance decreased by ${improvement.abs().toStringAsFixed(1)}%');
    }
    buffer.writeln('');
    
    buffer.writeln('🎯 Final Status:');
    if (results.overallSuccessRate >= 90) {
      buffer.writeln('🎉 EXCELLENT: Pulse group messaging is working optimally!');
    } else if (results.overallSuccessRate >= 75) {
      buffer.writeln('✅ GOOD: Pulse group messaging is working well with minor issues.');
    } else if (results.overallSuccessRate >= 50) {
      buffer.writeln('⚠️ FAIR: Pulse group messaging has some issues that need attention.');
    } else {
      buffer.writeln('❌ POOR: Pulse group messaging has significant issues requiring immediate attention.');
    }
    
    return buffer.toString();
  }
}

/// Integration test results container
class IntegrationTestResults {
  late DiagnosticResults initialDiagnostic;
  late SyncFixResults fixResults;
  late DiagnosticResults postFixDiagnostic;
  bool messageDeliveryTest = false;
  bool enhancedSyncTest = false;
  bool multiUserTest = false;
  double overallSuccessRate = 0.0;

  void calculateOverallSuccess() {
    final scores = [
      initialDiagnostic.getSuccessRate(),
      fixResults.getSuccessRate(),
      postFixDiagnostic.getSuccessRate(),
      messageDeliveryTest ? 100.0 : 0.0,
      enhancedSyncTest ? 100.0 : 0.0,
      multiUserTest ? 100.0 : 0.0,
    ];
    
    overallSuccessRate = scores.reduce((a, b) => a + b) / scores.length;
  }

  bool get allTestsPassed => overallSuccessRate >= 90.0;
}
