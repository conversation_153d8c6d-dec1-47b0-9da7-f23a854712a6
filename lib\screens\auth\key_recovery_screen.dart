import 'package:flutter/material.dart';
import '../../services/secure_key_backup_service.dart';
import '../../models/key_backup.dart';

/// Screen for recovering encryption keys after app reinstallation
class KeyRecoveryScreen extends StatefulWidget {
  const KeyRecoveryScreen({super.key});

  @override
  State<KeyRecoveryScreen> createState() => _KeyRecoveryScreenState();
}

class _KeyRecoveryScreenState extends State<KeyRecoveryScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _recoveryPhraseController = TextEditingController();

  final _backupService = SecureKeyBackupService();

  late TabController _tabController;
  bool _isLoading = false;
  bool _obscurePassword = true;
  KeyRecoveryMethod _selectedMethod = KeyRecoveryMethod.password;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedMethod = _tabController.index == 0
            ? KeyRecoveryMethod.password
            : KeyRecoveryMethod.recoveryPhrase;
      });
    });
    _checkForExistingBackup();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _passwordController.dispose();
    _recoveryPhraseController.dispose();
    super.dispose();
  }

  /// Check if user has existing backup
  Future<void> _checkForExistingBackup() async {
    final hasBackup = await _backupService.hasExistingBackup();
    if (!hasBackup && mounted) {
      _showNoBackupDialog();
    }
  }

  /// Show dialog when no backup is found
  void _showNoBackupDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('No Backup Found'),
        content: const Text(
          'No encryption key backup was found for your account. '
          'This means your previous encrypted messages cannot be recovered. '
          'You can continue to use the app, but previous conversations will not be accessible.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to main app
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  /// Attempt to recover keys
  Future<void> _recoverKeys() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      bool success = false;

      if (_selectedMethod == KeyRecoveryMethod.password) {
        success = await _backupService.restoreKeysFromBackup(
          masterPassword: _passwordController.text.trim(),
        );
      } else {
        success = await _backupService.restoreKeysFromBackup(
          masterPassword: _recoveryPhraseController.text.trim(),
          recoveryPhrase: _recoveryPhraseController.text.trim(),
        );
      }

      if (success && mounted) {
        _showSuccessDialog();
      } else if (mounted) {
        _showErrorDialog(
            'Failed to recover keys. Please check your credentials and try again.');
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('An error occurred during recovery: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Show success dialog
  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Recovery Successful'),
        content: const Text(
          'Your encryption keys have been successfully recovered! '
          'You can now access your previous encrypted conversations.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to main app
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  /// Show error dialog
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Recovery Failed'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Recover Encryption Keys'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header
                Icon(
                  Icons.security_rounded,
                  size: 64,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(height: 16),
                Text(
                  'Recover Your Keys',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'Enter your backup credentials to restore access to your encrypted messages',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),

                // Tab bar
                TabBar(
                  controller: _tabController,
                  tabs: const [
                    Tab(text: 'Password'),
                    Tab(text: 'Recovery Phrase'),
                  ],
                ),
                const SizedBox(height: 24),

                // Tab content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildPasswordTab(),
                      _buildRecoveryPhraseTab(),
                    ],
                  ),
                ),

                // Recovery button
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _isLoading ? null : _recoverKeys,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('Recover Keys'),
                ),

                const SizedBox(height: 16),

                // Skip recovery button
                TextButton(
                  onPressed: _isLoading
                      ? null
                      : () {
                          Navigator.of(context).pop();
                        },
                  child: const Text('Skip Recovery'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build password recovery tab
  Widget _buildPasswordTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          'Enter Backup Password',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _passwordController,
          decoration: InputDecoration(
            labelText: 'Backup Password',
            hintText: 'Enter your backup password',
            border: const OutlineInputBorder(),
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility : Icons.visibility_off,
              ),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
          ),
          obscureText: _obscurePassword,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your backup password';
            }
            if (value.length < 8) {
              return 'Password must be at least 8 characters';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        Text(
          'This is the password you set when creating your key backup.',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
        ),
      ],
    );
  }

  /// Build recovery phrase tab
  Widget _buildRecoveryPhraseTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          'Enter Recovery Phrase',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _recoveryPhraseController,
          decoration: const InputDecoration(
            labelText: 'Recovery Phrase',
            hintText: 'Enter your 12-word recovery phrase',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your recovery phrase';
            }
            if (!_backupService.validateRecoveryPhrase(value.trim())) {
              return 'Invalid recovery phrase format';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        Text(
          'Enter the 12-word recovery phrase you saved when setting up key backup.',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
        ),
      ],
    );
  }
}
